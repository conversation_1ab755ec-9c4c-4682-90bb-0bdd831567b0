{"features": [{"custom": {"numberOfTapsToTrigger": 4}, "identifier": "airshipChannelIdDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["app://settings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"accountUrl": "customer", "loginUrl": "app://login", "logoutUrl": "auth/logout", "secretIdentifier": "navigationApiKey", "serviceEndpoint": "navigation/service", "showAsLoggedIn": false, "staticEntriesBottom": [{"children": [{"l10n": "account.settings.title", "type": "link", "url": "app://settings"}, {"l10n": "account.rate.title", "type": "link", "url": "https://apps.apple.com/app/id1436271825?action=write-review"}, {"l10n": "navigation.recommendation.title", "type": "link", "url": "app://recommendation"}], "l10n": "account.settings.sectionTitle", "type": "section"}], "staticEntriesTop": [], "supportedUrls": ["app://openAccount", "app://openAccount/service", "app://account"]}, "identifier": "account", "isEnabled": true}, {"custom": {"appId": "<PERSON><PERSON>", "collectorUrl": "https://eu-witt-gruppe-prod1.mini.snplow.net", "namespace": "spNative", "pushPath": "36cfe/zc9"}, "identifier": "snowplow", "isEnabled": true}, {"custom": {"eventIdMapping": {}, "eventTokenMapping": {"InteractionAddToCart": "31oy60", "InteractionLoggedIn": "9k0m4t", "InteractionPurchase": "kaag6i", "ScreenCheckout": "7wbd6w", "ScreenProductDetailPage": "lrv2va", "ScreenProductList": "ytl5gw"}, "uninstall": true, "urlPrefix": "https://8auq.adj.st/"}, "identifier": "adjustTracking", "isEnabled": true}, {"custom": {"cloudSite": "eu"}, "identifier": "airship", "isEnabled": true}, {"custom": {"attributeIdMapping": {}, "eventIdMapping": {"basket_badge_count": true, "purchase_completed": true, "user_info": true, "wishlist_badge_count": true}, "eventTokenMapping": {"InteractionAddToCart": "31oy60", "InteractionLoggedIn": "9k0m4t", "ScreenCheckout": "7wbd6w", "ScreenProductDetailPage": "lrv2va", "ScreenProductList": "ytl5gw"}, "keepContactAssociation": true}, "identifier": "airshipTracking", "isEnabled": true}, {"custom": {"domains": []}, "identifier": "allowedDomains", "isEnabled": true}, {"custom": {"actionText": null, "bodyText": null, "externalTargetUrl": "itms-apps://itunes.apple.com/app/id1436271825", "headlineText": null, "isAllowedToStart": true}, "identifier": "appUpdate", "isEnabled": false}, {"custom": {"assortmentEndpoint": "navigation/assortment", "displayBanners": true, "staticEntriesBottom": [{"children": [{"l10n": "assortment.orderForm.title", "teaser": {"imageName": "assortmentSecondLevelSecondaryTile1", "style": {"class": "square"}}, "type": "link", "url": "/directorderform"}], "l10n": "assortment.services.title", "type": "section"}], "staticEntriesTop": [], "supportedUrls": ["app://assortment"]}, "identifier": "assortment", "isEnabled": true}, {"custom": {"api": "", "web": ""}, "identifier": "baseUrl", "isEnabled": true}, {"custom": {"configEndpoint": null, "joinRecommendationWithInfoString": null, "measureUrl": null, "recommendationEndpoint": null, "secretIdentifier": "", "supportedUrls": ["app://openBraFittingGuide"]}, "identifier": "braFittingGuide", "isEnabled": false}, {"custom": {"catalogEndpoint": "", "catalogOrderUrl": "", "secretIdentifier": "", "supportedUrls": ["app://openCatalogScanner"]}, "identifier": "catalogScanner", "isEnabled": false}, {"custom": {"customTabUrls": [], "exitUrls": [], "showExternalUrlsInWebView": false, "successUrls": [], "supportedUrls": [], "webViewUrls": []}, "identifier": "checkout", "isEnabled": false}, {"custom": {"enableLogs": true}, "identifier": "crashReporting", "isEnabled": true}, {"custom": {"apiUrl": "https://yll-nl.aac.ninja/api", "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["app://deals"]}, "identifier": "deals", "isEnabled": true}, {"custom": {"matchers": ["regex:.*\\.csv", "regex:.*\\.pdf", "regex:.*\\/pdf\\/"], "mimeType": ["application/pdf"]}, "identifier": "download", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.abnamro\\.nl.*$", "regex:^https:\\/\\/betalen\\.rabobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.ing\\.nl.*$", "regex:^https:\\/\\/diensten\\.asnbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.snsbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.regiobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.bunq\\.com.*$", "regex:^https:\\/\\/ideal\\.triodos\\.nl.*$", "mailto:", "https://apps.apple.com/app/id1436271825?action=write-review", "regex:.*\\.facebook\\.*"]}, "identifier": "externalBrowser", "isEnabled": true}, {"custom": {}, "identifier": "firebaseTracking", "isEnabled": true}, {"custom": {"imageEndpoint": "https://yll-nl.aac.ninja/image-v2/scaled?scaleUp=false&bucketPower=2", "secretIdentifier": "appConfigToken"}, "identifier": "imageAPI", "isEnabled": true}, {"custom": {"supportedUrls": ["https://www.your-look-for-less.nl/images/content/sa/pdf/sase_agb.pdf", "https://www.myhermes.de/paketshop", "https://www.myhermes.de", "myhermes.de/wps/portal/paket/Home/privatkunden", "www.ec.europa.eu"]}, "identifier": "inAppBrowser", "isEnabled": true}, {"custom": {"deleteMessagesAfterDays": 30, "deleteMessagesAfterTenantChange": true, "forceMessageWebView": false, "shouldShowThumbnails": true, "supportedUrls": ["app://openInbox", "app://inbox/overview"]}, "identifier": "inbox", "isEnabled": true}, {"custom": {"showTabBarBadge": true, "supportedUrls": ["app://login?trigger=app_new_install", "app://login"], "webPath": "customer#customerLoginModal"}, "identifier": "login", "isEnabled": true}, {"custom": {"navigationTitle": {"enabledUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?your-look-for-less\\.nl(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]}}, "identifier": "loginButton", "isEnabled": true}, {"custom": {"appendQueryParameterToDeeplinks": null, "login": "app://login?trigger=app_new_install", "registration": "customer", "showLoginButton": true, "showRegisterButton": false, "toShop": "?trigger=app_new_install"}, "identifier": "onboarding", "isEnabled": true}, {"custom": {"promoPattern": "", "webbridgeTrigger": false}, "identifier": "promoBanner", "isEnabled": false}, {"custom": {"defaultNotificationChannel": null, "defaultOptIn": true, "deliveryStatusItemEnabled": false, "openSystemSettingsFromAppSettings": false, "showAndroid13OptInDialog": true, "showOptInDialog": false, "showOptInDialogAfterCheckout": false}, "identifier": "push", "isEnabled": true}, {"custom": {"supportedUrls": ["app://showPushDialog"]}, "identifier": "pushDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:app:\\/\\/pushPromotionLayer(\\?.*)?"]}, "identifier": "pushPromotion", "isEnabled": true}, {"custom": {"showAfterDays": 90}, "identifier": "pushPromotionBanner", "isEnabled": true}, {"custom": {"message": "", "supportedUrls": ["app://openRecommendation", "app://recommendation"], "url": "https://app.adjust.com/2it2gyw"}, "identifier": "recommendation", "isEnabled": true}, {"custom": {"supportedUrls": ["app://review", "app://openRating"]}, "identifier": "review", "isEnabled": true}, {"custom": {"": ""}, "identifier": "screenTracking", "isEnabled": true}, {"custom": {"debounceMillis": 500, "maxHistoryItems": 5, "minCharacters": 2, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "suggestionsApiUrl": "https://yll-nl.aac.ninja/api/search/suggestions?query=", "supportedUrls": ["app://search"], "webPath": "/search?q="}, "identifier": "search", "isEnabled": true}, {"custom": {"clearSessionCookieOnAppStart": false, "sessionTimeout": 0}, "identifier": "session", "isEnabled": true}, {"custom": {"regionLowerLeftLatitude": 45.68858, "regionLowerLeftLongitude": 5.91474, "regionUpperRightLatitude": 55.25106, "regionUpperRightLongitude": 17.1109, "storesEndpoint": "https://yll-nl.aac.ninja/v1/shops/", "supportedUrls": ["app://openShopFinder"]}, "identifier": "storeFinder", "isEnabled": false}, {"custom": {"hideSearchOnTabs": ["basket", "wishlist", "account"], "noBackstackInTabs": [], "tabRouting": [{"allowedIds": ["cart", "basket"], "urls": ["basket.*"]}, {"allowedIds": ["wishlist"], "urls": ["wishlist.*"]}, {"allowedIds": ["profile", "account"], "urls": ["customer.*"]}, {"allowedIds": ["assortment"], "urls": ["regex:^app:\\/\\/assortment\\/.*"]}, {"allowedIds": ["shop"], "urls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?your-look-for-less\\.nl(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "auth/logout.*"]}, {"allowedIds": ["shop", "cart", "wishlist", "assortment", "account", "basket", "profile"], "urls": ["regex:^https?:\\/\\/.*\\.your-look-for-less\\.nl.*", "app://login"]}], "tabs": [{"activeImageName": "icon24x24TabBarActiveTab1", "hasLogo": true, "identifier": "shop", "inactiveImageName": "icon24x24TabBarInactiveTab1", "l10n": "navigation.shop.title", "url": "/"}, {"activeImageName": "icon24x24TabBarActiveTab2", "identifier": "assortment", "inactiveImageName": "icon24x24TabBarInactiveTab2", "l10n": "navigation.assortment.title", "url": "app://assortment"}, {"activeImageName": "icon24x24TabBarActiveTab3", "identifier": "wishlist", "inactiveImageName": "icon24x24TabBarInactiveTab3", "l10n": "navigation.wishlist.title", "url": "wishlist"}, {"activeImageName": "icon24x24TabBarActiveTab4", "identifier": "basket", "inactiveImageName": "icon24x24TabBarInactiveTab4", "l10n": "navigation.cart.title", "url": "basket"}, {"activeImageName": "icon24x24TabBarActiveTab5", "identifier": "account", "inactiveImageName": "icon24x24TabBarInactiveTab5", "l10n": "navigation.profile.title", "url": "app://account"}]}, "identifier": "tabBar", "isEnabled": true}, {"custom": {"ignoreSettingsTrackingWebbridge": false, "onlyGlobalOptIn": false, "requestATT": false, "showPreConsent": false, "viewEventMapping": {"Basket": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/basket\\/?(\\?.*)?$"], "Checkout": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/checkout(#\\/.*)?\\/?(\\?.*)?$"], "Home": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl(\\/|\\/?\\?logout)?$"], "Login": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/(checkout#\\/auth|customer#customerLoginModal|(.*)#headerAuthModal)$"], "OrderConfirmation": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/thanks\\/?(\\?.*)?$"], "ProductDetailPage": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/p\\/[0-9]+\\/?(\\?.*)?$"], "ProductList": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/(?!content\\/|p\\/|contact|\\??logout|checkout|search|thanks|checkout|customer|basket|wishlist|catalog|directorderform|.*(\\?|&)searchOrigin=|.*#headerAuthModal).+$"], "ProductListOfSearchResult": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/(search\\?q=|(?!p\\/).*\\?searchOrigin=).*$"], "Registration": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/checkout#\\/auth$"], "Wishlist": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/wishlist\\/?(\\?.*)?$"]}}, "identifier": "tracking", "isEnabled": true}, {"custom": {"basket": "", "privacyPolicy": "", "profile": "", "wishList": ""}, "identifier": "urlMapping", "isEnabled": true}, {"custom": {"isAdjustEnabled": true, "isAdvertisingIdEnabled": true, "isAirshipEnabled": true}, "identifier": "userAgent", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/wishlist\\/?(\\?.*)?$", "regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/checkout(#\\/.*)?\\/?(\\?.*)?$"]}, "identifier": "copyCodeBanner", "isEnabled": true}, {"custom": {"salutationUrls": ["regex:^https:\\/\\/(www\\.)?your-look-for-less\\.nl\\/(\\?.*)?$"]}, "identifier": "salutation", "isEnabled": true}, {"custom": {"blockedUrls": [], "disableSwipeToRefresh": [], "disabledBackButtonUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?your-look-for-less\\.nl(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "emitFakePageEventsForCSR": true, "enableLargeScreenOptimization": [], "forceLogo": false, "logoDisplayString": "", "maxWebViewSavedStateSize": 80000, "pdfUrls": ["regex:^https:\\/\\/www\\.your-look-for-less\\.nl\\/download\\/invoice\\/.*"], "redirectUrls": ["regex:^https:\\/\\/www\\.abnamro\\.nl.*$", "regex:^https:\\/\\/betalen\\.rabobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.ing\\.nl.*$", "regex:^https:\\/\\/diensten\\.asnbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.snsbank\\.nl.*$", "regex:^https:\\/\\/diensten\\.regiobank\\.nl.*$", "regex:^https:\\/\\/ideal\\.bunq\\.com.*$", "regex:^https:\\/\\/ideal\\.triodos\\.nl.*$", "regex:^https:\\/\\/([A-Za-z0-9]+\\.)?your-look-for-less\\.nl(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "reloadOnAppearUrls": ["/customer"], "restrictWebViewSavedStateSize": true, "sharingType": "DIRECT", "supportedUrls": ["^https.*"]}, "identifier": "web", "isEnabled": true}, {"custom": {"behaviors": [{"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=screenviews"}, "conditions": [{"period": 10, "type": "screenViews"}], "disabledUrls": ["onboarding", "login"], "id": "push", "maxInvocations": 1, "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=orderconfirmation"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "disabledUrls": ["onboarding"], "id": "pushPurchaseCompleted", "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"start": 10, "type": "appStarts"}], "id": "review", "maxInvocations": 1, "precondition": "none"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "review", "precondition": "none"}], "debounceMs": 500}, "identifier": "coordinator", "isEnabled": true}]}