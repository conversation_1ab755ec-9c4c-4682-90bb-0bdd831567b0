// swiftlint:disable all
// periphery:ignore:all
// Generated using OGAssetFetcher
import Foundation
import UICatalog
// swiftlint:disable superfluous_disable_command
// swiftlint:disable file_length
struct OGFontResolver: OGFontResolvable {
  public func font(_ key: OGFonts) -> OGFontStyle {
    switch key {
      case .headlineXXXL:
        return OGFontStyle(name: "Assistant-<PERSON>", size: 36, lineHeight: 40)
      case .headlineXXL:
        return OGFontStyle(name: "Assistant-Bold", size: 32, lineHeight: 44)
      case .headlineLRegular:
        return OGFontStyle(name: "Assistant-Regular", size: 18, lineHeight: 26)
      case .headlineLEmphasized:
        return OGFontStyle(name: "Assistant-<PERSON>", size: 18, lineHeight: 26)
      case .headlineXL:
        return OGFontStyle(name: "Assistant-Bold", size: 24, lineHeight: 25)
      case .titleM:
        return OGFontStyle(name: "Assistant-<PERSON>", size: 16, lineHeight: 20)
      case .titleS:
        return OGFontStyle(name: "Assistant-Bold", size: 14, lineHeight: 24)
      case .copyXL:
        return OGFontStyle(name: "Assistant-Regular", size: 24, lineHeight: 25)
      case .copyL:
        return OGFontStyle(name: "Assistant-Regular", size: 16, lineHeight: 20)
      case .copyMRegular:
        return OGFontStyle(name: "Assistant-Regular", size: 14, lineHeight: 22)
      case .copyS:
        return OGFontStyle(name: "Assistant-Regular", size: 12, lineHeight: 20)
      case .section:
        return OGFontStyle(name: "Assistant-Bold", size: 13, lineHeight: 18)
      case .buttonLabelM:
        return OGFontStyle(name: "Assistant-Bold", size: 14, lineHeight: 24)
      case .buttonLabelS:
        return OGFontStyle(name: "Assistant-Bold", size: 12, lineHeight: 20)
      case .label:
        return OGFontStyle(name: "Assistant-Bold", size: 12, lineHeight: 20)
      case .footnote:
        return OGFontStyle(name: "Assistant-Regular", size: 13, lineHeight: 16)
      case .tabnavigation:
        return OGFontStyle(name: "Assistant-Bold", size: 10, lineHeight: 16)
      case .caption:
        return OGFontStyle(name: "Assistant-Regular", size: 12, lineHeight: 16)
      case .copyMEmphasized:
        return OGFontStyle(name: "Assistant-Bold", size: 14, lineHeight: 22)
      case .priceL:
        return OGFontStyle(name: "Assistant-SemiBold", size: 18, lineHeight: 26)
      case .priceSEmphasized:
        return OGFontStyle(name: "Assistant-Bold", size: 14, lineHeight: 22)
      case .priceSRegular:
        return OGFontStyle(name: "Assistant-Regular", size: 14, lineHeight: 22)
      case .priceMRegular:
        return OGFontStyle(name: "Assistant-Regular", size: 16, lineHeight: 20)
      case .priceMEmphasized:
        return OGFontStyle(name: "Assistant-Bold", size: 16, lineHeight: 20)
    }
  }
}
