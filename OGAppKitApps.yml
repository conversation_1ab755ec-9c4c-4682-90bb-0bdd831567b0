---
BON:
  name: bonprix
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bon.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bon.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.bon.alpha, match
        InHouse com.og1o.bon.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bon.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.bon.beta, match
        InHouse com.og1o.bon.beta.NotificationService
      FIREBASE_QA_ID: bonprix_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 2G7ULLGT8C
      PRODUCT_BUNDLE_IDENTIFIER: pl.bonprix.bonprix
      CODE_SIGN_IDENTITY: 'Apple Distribution: bonprix Handelsgesellschaft mbH (2G7ULLGT8C)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore pl.bonprix.bonprix **********, match AppStore
        pl.bonprix.bonprix.NotificationService **********
      APP_ID: **********
      TEAM_ID: 214173
      SHORT_NAME: BonPrixHandelsgesellschaftmbH
      MATCH_BRANCH: bonprix
BPX:
  name: bonprix
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bpx.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bpx.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.bpx.alpha, match
        InHouse com.og1o.bpx.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.bpx.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.bpx.beta,
        match InHouse com.og1o.bpx.beta.NotificationService
      FIREBASE_QA_ID: bonprix_qa, bonprix_FIN_ES
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 2G7ULLGT8C
      PRODUCT_BUNDLE_IDENTIFIER: es.bonprix.bonprix
      CODE_SIGN_IDENTITY: 'Apple Distribution: bonprix Handelsgesellschaft mbH (2G7ULLGT8C)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore es.bonprix.bonprix **********,
        match AppStore es.bonprix.bonprix.NotificationService **********
      APP_ID: **********
      TEAM_ID: 214173
      SHORT_NAME: AKX5JY3M5D
      MATCH_BRANCH: bonprix
CRE:
  name: creation L
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.cre.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.cre.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.cre.alpha, match
        InHouse com.og1o.cre.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.cre.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.cre.beta,
        match InHouse com.og1o.cre.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: eu.witt-gruppe.creationl
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore eu.witt-gruppe.creationl **********,
        match AppStore eu.witt-gruppe.creationl.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
HEI:
  name: heine
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.hei.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.hei.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.hei.alpha,
        match InHouse com.og1o.hei.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.hei.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.hei.beta,
        match InHouse com.og1o.hei.beta.NotificationService
      FIREBASE_QA_ID: heine_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: de.heine.mobile
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore de.heine.mobile **********,
        match AppStore de.heine.mobile.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
LAS:
  name: LASCANA
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.las.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.las.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.las.alpha, match
        InHouse com.og1o.las.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.las.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.las.beta, match
        InHouse com.og1o.las.beta.NotificationService
      FIREBASE_QA_ID: lascana_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 569YPR5A6K
      PRODUCT_BUNDLE_IDENTIFIER: de.otto.lascana
      CODE_SIGN_IDENTITY: 'Apple Distribution: OTTO (GmbH & Co KG) (569YPR5A6K)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore de.otto.lascana, match
        AppStore de.otto.lascana.NotificationService
      APP_ID: **********
      TEAM_ID: 339110
      SHORT_NAME: OTTOGmbHCoKG
      MATCH_BRANCH: lascana
MAN:
  name: Manufactum
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.man.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.man.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER:
        match InHouse com.og1o.man.alpha,
        match InHouse com.og1o.man.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.man.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER:
        match InHouse com.og1o.man.beta,
        match InHouse com.og1o.man.beta.NotificationService
      FIREBASE_QA_ID: manufactum_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: BP2B4N28DH
      PRODUCT_BUNDLE_IDENTIFIER: de.manufactum.manufactum
      NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER: de.manufactum.manufactum.PushNotificationService
      CODE_SIGN_IDENTITY: "Apple Distribution: Manufactum GmbH (Apps) (BP2B4N28DH)"
      PROVISIONING_PROFILE_SPECIFIER: match AppStore de.manufactum.manufactum,
        match AppStore de.manufactum.manufactum.PushNotificationService
      APP_ID: **********
      TEAM_ID: 127510798
      SHORT_NAME: BP2B4N28DH
      MATCH_BRANCH: manufactum
SAN:
  name: Sieh an!
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.san.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.san.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.san.alpha, match
        InHouse com.og1o.san.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.san.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.san.beta, match
        InHouse com.og1o.san.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: eu.witt-gruppe.siehan
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore eu.witt-gruppe.siehan **********,
        match AppStore eu.witt-gruppe.siehan.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
SHE:
  name: sheego
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.she.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.she.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.she.alpha, match
        InHouse com.og1o.she.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.she.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.she.beta, match
        InHouse com.og1o.she.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: de.sheego.mobile
      NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER: de.sheego.mobile.PushNotificationService
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore de.sheego.mobile,
        match AppStore de.sheego.mobile.PushNotificationService
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
WIT:
  name: Witt
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.wit.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.wit.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.wit.alpha, match
        InHouse com.og1o.wit.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.wit.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.wit.beta, match
        InHouse com.og1o.wit.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: eu.witt-gruppe.witt
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore eu.witt-gruppe.witt **********,
        match AppStore eu.witt-gruppe.witt.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
YLFLNL:
  name: 'YLFL '
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflnl.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflnl.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.ylflnl.alpha,
        match InHouse com.og1o.ylflnl.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflnl.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.ylflnl.beta,
        match InHouse com.og1o.ylflnl.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: eu.witt-gruppe.yourlookforlessnl
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore eu.witt-gruppe.yourlookforlessnl **********,
        match AppStore eu.witt-gruppe.yourlookforlessnl.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
YLFLSE:
  name: YLFL
  version: 6.3.0
  configs:
    Alpha (Debug):
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflse.alpha
    Alpha:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Dev
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflse.alpha
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.ylflse.alpha,
        match InHouse com.og1o.ylflse.alpha.NotificationService
    Beta:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Beta
      DEVELOPMENT_TEAM: 8YGSY7F4JR
      PRODUCT_BUNDLE_IDENTIFIER: com.og1o.ylflse.beta
      CODE_SIGN_IDENTITY: 'iPhone Distribution: Otto Group Solution Provider (OSP) GmbH'
      PROVISIONING_PROFILE_SPECIFIER: match InHouse com.og1o.ylflse.beta,
        match InHouse com.og1o.ylflse.beta.NotificationService
      FIREBASE_QA_ID: witt_qa
    Release:
      ASSETCATALOG_COMPILER_APPICON_NAME: launcher1024x1024Plain
      DEVELOPMENT_TEAM: 76NJWR3Q3T
      PRODUCT_BUNDLE_IDENTIFIER: eu.witt-gruppe.yourlookforlessse
      CODE_SIGN_IDENTITY: 'Apple Distribution: Josef Witt GmbH (76NJWR3Q3T)'
      PROVISIONING_PROFILE_SPECIFIER: match AppStore eu.witt-gruppe.yourlookforlessse **********,
        match AppStore eu.witt-gruppe.yourlookforlessse.NotificationService **********
      APP_ID: **********
      TEAM_ID: 118902527
      SHORT_NAME: 76NJWR3Q3T
      MATCH_BRANCH: witt
