# Generated with bin/generate-deployment.sh

name: Build and Deploy iOS

on:
  #push:
  #  branches: [ main ]
  #pull_request:
  #  branches: [ main ]

  workflow_dispatch:
    inputs:
      target:
        description: "App"
        required: true
        type: choice
        options:
        - BON
        - BPX
        - CRE
        - HEI
        - LAS
        - MAN
        - SAN
        - SHE
        - WIT
        - YLFLNL
        - YLFLSE
      config:
        description: "Build"
        required: true
        type: choice
        options:
        - Alpha
        - Beta
        - Release
      deployment_destination: 
        description: "Deployment destination"
        required: true
        type: choice
        options:
        - Firebase
        - FirebaseAndSaucelabs
        - Saucelabs
        - Testflight

jobs:
  ios-deployment:
    name: iOS Deployment
    runs-on: macos-15
    steps:
      - id: xcode-setup
        name: Setup Xcode version
        uses: maxim-lobanov/setup-xcode@v1.6.0
        with:
          xcode-version: '16.4'
      - name: Apply netrc creds with a JSON block
        uses: bcomnes/netrc-creds@v3
        with:
          creds: |
            [
              {
                "machine": "github.com",
                "login": "og-dx-aac-ci",
                "password": "${{ secrets.CI_GITHUB_TOKEN }}"
              },
              {
                "machine": "maven.pkg.github.com",
                "login": "${{ secrets.GH_PACKAGES_USERNAME }}",
                "password": "${{ secrets.GH_PACKAGES_TOKEN }}"
              }
            ]
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Setting fetch_depth to 0 provides the whole project history and enables comparison between latest tag ad head used for versioning and commit history
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: 3.1
          bundler-cache: true
      - name: Install SSH key
        uses: shimataro/ssh-key-action@v2
        with:
          key: ${{ secrets.OGKIT_SSH_PRIVATE_KEY }}
          name: id_rsa # optional
          known_hosts: ${{ secrets.KNOWN_HOSTS_OGKIT }}
          if_key_exists: fail # replace / ignore / fail; optional (defaults to fail)
      - name: Cache SPMs
        uses: actions/cache@v4
        with:
          path: .build
          key: ${{ runner.os }}-spm-${{ hashFiles('**/Package.resolved') }}
          restore-keys: |
            ${{ runner.os }}-spm-
      - name: Deploy to Firebase
        if: github.event.inputs.deployment_destination == 'Firebase'
        run: |
          make deploy target=${{ github.event.inputs.target }} config=${{ github.event.inputs.config }} upload_to_saucelabs=false
      - name: Deploy to Saucelabs
        if: github.event.inputs.deployment_destination == 'Saucelabs' && github.event.inputs.config == 'Beta'
        run: |
          make build-beta-saucelabs target=${{ github.event.inputs.target }} UPLOADTOSAUCELABS=true
      - name: Deploy to FirebaseAndSaucelabs
        if: github.event.inputs.deployment_destination == 'FirebaseAndSaucelabs' && github.event.inputs.config == 'Beta'
        run: |
          make deploy target=${{ github.event.inputs.target }} config=${{ github.event.inputs.config }} upload_to_saucelabs=true
      - name: Deploy to Testflight
        if: github.event.inputs.deployment_destination == 'Testflight' && github.event.inputs.config == 'Release'
        run: |
          make deploy target=${{ github.event.inputs.target }} config=${{ github.event.inputs.config }} upload_to_saucelabs=false
      - name: Invalid options
        if: (github.event.inputs.deployment_destination == 'Saucelabs' || github.event.inputs.deployment_destination == 'FirebaseAndSaucelabs') && github.event.inputs.config != 'Beta'
        run: |
          echo "Only Beta config is supported for Saucelabs deployment"
          exit 1
      - name: Clean Archives directory
        run: |
          rm -rf ~/Library/Developer/Xcode/Archives/*
    env:
      BUNDLE_PATH: .vendor/bundle
      SAUCE_USERNAME: ${{ secrets.SAUCE_USERNAME }}
      SAUCE_ACCESS_KEY: ${{ secrets.SAUCE_ACCESS_KEY }}
      SECRET_KEYCHAIN_PASSWORD: ${{ secrets.SECRET_KEYCHAIN_PASSWORD }}
      SECRET_KEYSTORE_PASSWORD: ${{ secrets.SECRET_KEYSTORE_PASSWORD }}
      FASTLANE_PASSWORD: ${{ secrets.FASTLANE_PASSWORD }}
      FASTLANE_USER: ${{ secrets.FASTLANE_USER }}
      FIREBASE_CLI_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
      MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
      MATCH_KEYCHAIN_PASSWORD: ${{ secrets.MATCH_KEYCHAIN_PASSWORD }}
      MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}
      SLACK_URL: ${{ secrets.SLACK_HOOK_URL }}
      FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD: ${{ secrets.FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD }}      
      CI_GITHUB_TOKEN: ${{ secrets.CI_GITHUB_TOKEN }}
      TERM: xterm-256color
      HOMEBREW_NO_INSTALL_CLEANUP: TRUE
      FASTLANE_DONT_STORE_PASSWORD: 1
      FASTLANE_HIDE_CHANGELOG: true
      FASTLANE_SKIP_UPDATE_CHECK: true
      FASTLANE_XCODE_LIST_TIMEOUT: 60
      FASTLANE_XCODEBUILD_SETTINGS_TIMEOUT: 60