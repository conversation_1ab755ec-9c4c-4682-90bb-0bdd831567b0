import Login
import OGN<PERSON>gationBar
import SwiftUI
import UICatalog

// MARK: - AccountView

struct AccountView: View {
  @StateObject private var viewStore: Self.Store

  private let title: String

  public init(title: String) {
    _viewStore = StateObject(wrappedValue: Self.make())
    self.title = title
  }

  var body: some View {
    OGNavigationBar(titleView: navigationTitleView, content: {
      content
    })
  }

  private var content: some View {
    ZStack(alignment: .bottom) {
      rowsList

      if viewStore.shouldShowLogInButton {
        LoginButtonView()
          .padding(UILayoutConstants.AccountView.defaultPadding)
          .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
      }
    }
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear)
      }
    }
  }

  private var navigationTitleView: some View {
    Text(title)
      .accessibilitySortPriority(3)
  }

  private var rowsList: some View {
    ScrollView {
      if !viewStore.shouldShowLogInButton {
        AccountRow()
          .padding(UILayoutConstants.AccountView.topDefaultPadding)
      }
      VStack(spacing: .zero) {
        ForEach(viewStore.staticEntriesTop, id: \.self) { row in
          row.makeView()
        }
        ForEach(viewStore.rows, id: \.self) { row in
          row.makeView()
        }
        ForEach(viewStore.staticEntriesBottom, id: \.self) { row in
          row.makeView()
        }
        if !viewStore.shouldShowLogInButton {
          LogoutRow()
          Divider()
            .background(OGColors.backgroundBackground20.color)
            .padding(UILayoutConstants.AccountView.dividerPadding)
        }
        detectiveRow

        if viewStore.shouldShowLogInButton {
          Spacer()
            .frame(height: UILayoutConstants.AccountView.loginButtonHeight)
        }
      }
    }
  }

  @ViewBuilder private var detectiveRow: some View {
    if viewStore.shouldShowDetective {
      Button {
        Task {
          await viewStore.dispatch(.didPressDetective)
        }
      } label: {
        HStack {
          Label("Detective", systemImage: "ladybug")
            .foregroundColor(OGColors.backgroundBackground100.color)
            .font(for: .copyMRegular)
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(UILayoutConstants.AccountView.textPadding)
          Spacer()
        }
        .background(OGColors.backgroundBackground0.color)
      }
      .buttonStyle(.plain)
      Divider()
        .background(OGColors.backgroundBackground20.color)
        .padding(UILayoutConstants.AccountView.dividerPadding)
    }
  }
}

extension AccountView.Row {
  @ViewBuilder
  func makeView() -> some View {
    switch self {
    case let .navigationEntry(entry):
      AccountNavigationEntryView(entry: entry)
    }
  }
}

// MARK: - UILayoutConstants.AccountView

extension UILayoutConstants {
  enum AccountView {
    static let minHeight = 44.0
    static let sectionTitleHeight = 38.0
    static let loginButtonHeight = 66.0
    static let defaultPadding = EdgeInsets(
      top: UILayoutConstants.Default.padding2x,
      leading: UILayoutConstants.Default.padding2x,
      bottom: UILayoutConstants.Default.padding2x,
      trailing: UILayoutConstants.Default.padding2x
    )
    static let topDefaultPadding = EdgeInsets(
      top: UILayoutConstants.Default.padding2x,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 0,
      trailing: UILayoutConstants.Default.padding2x
    )
    static let headerTextPadding = EdgeInsets(
      top: 12.0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: UILayoutConstants.Default.padding,
      trailing: UILayoutConstants.Default.padding2x
    )
    static let textPadding = EdgeInsets(
      top: 11.0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 11.0,
      trailing: UILayoutConstants.Default.padding
    )
    static let accessoryPadding = EdgeInsets(
      top: 10.0,
      leading: 0,
      bottom: 10.0,
      trailing: 14.0
    )
    static let dividerPadding = EdgeInsets(
      top: 0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 0,
      trailing: 0
    )
  }
}
