import AppCore
import Combine
import Foundation
import OGAppEnvironment
import OGCore
import OGDomainStore
import OGFeatureKit
import OGL10n
import OGRouter
import OGTracker
import OGViewStore
import SwiftUI
import TenantChooser
import UICatalog

// MARK: - AccountView.Row

extension AccountView {
  enum Row: Equatable, Hashable {
    case navigationEntry(AccountNavigationEntry)

    func hash(into hasher: inout Hasher) {
      switch self {
      case let .navigationEntry(entry):
        hasher.combine(entry)
      }
    }
  }
}

// MARK: - AccountView.Store

extension AccountView {
  typealias Store = OGViewStore<ViewState, Event>
}

extension AccountView {
  static func make() -> Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - AccountView.Event

extension AccountView {
  public enum Event: OGViewEvent, Hashable {
    /// Public Events
    case didPressUrl(urlString: String)
    case didPressDetective
    case didAppear

    /// Private Events
    case _addNavigationEntries([AccountNavigationEntry])
    case _addStaticNavigationEntries(top: [AccountNavigationEntry], bottom: [AccountNavigationEntry])
    case _didLogIn
    case _didLogout
    case _showAsLoggedIn(value: Bool)
    case _updateDetectiveVisibility(Bool)
    case _trackDidAppear
  }
}

// MARK: - AccountView.ViewState

extension AccountView {
  public struct ViewState: OGViewState {
    private(set) var rows: [AccountView.Row]
    private(set) var staticEntriesTop: [AccountView.Row]
    private(set) var staticEntriesBottom: [AccountView.Row]
    private(set) var shouldShowLogInButton: Bool
    private(set) var shouldShowDetective: Bool
    private(set) var showAsLoggedIn: Bool

    public static var initial: AccountView.ViewState = .init()

    init(
      accountFeatureAdapter: AccountFeatureAdaptable = AccountFeatureAdapterContainer.shared.account(),
      rows: [AccountView.Row] = [],
      shouldShowLogInButton: Bool = true,
      shouldShowDetective: Bool = false
    ) {
      self.rows = rows
      self.staticEntriesTop = accountFeatureAdapter.configuration.value.staticEntriesTop.map { .navigationEntry($0) }
      self.staticEntriesBottom = accountFeatureAdapter.configuration.value.staticEntriesBottom.map { .navigationEntry($0) }
      self.shouldShowLogInButton = shouldShowLogInButton
      self.shouldShowDetective = shouldShowDetective
      self.showAsLoggedIn = accountFeatureAdapter.configuration.value.showAsLoggedIn
    }

    public init(
      rows: [AccountView.Row],
      staticEntriesTop: [AccountView.Row],
      staticEntriesBottom: [AccountView.Row],
      shouldShowLogInButton: Bool,
      shouldShowDetective: Bool,
      showAsLoggedIn: Bool
    ) {
      self.rows = rows
      self.staticEntriesTop = staticEntriesTop
      self.staticEntriesBottom = staticEntriesBottom
      self.shouldShowLogInButton = shouldShowLogInButton
      self.shouldShowDetective = shouldShowDetective
      self.showAsLoggedIn = showAsLoggedIn
    }

    func update(
      rows: [AccountView.Row]? = nil,
      staticEntriesTop: [AccountView.Row]? = nil,
      staticEntriesBottom: [AccountView.Row]? = nil,
      shouldShowLogInButton: Bool? = nil,
      shouldShowDetective: Bool? = nil,
      showAsLoggedIn: Bool? = nil
    ) -> ViewState {
      ViewState(
        rows: rows ?? self.rows,
        staticEntriesTop: staticEntriesTop ?? self.staticEntriesTop,
        staticEntriesBottom: staticEntriesBottom ?? self.staticEntriesBottom,
        shouldShowLogInButton: shouldShowLogInButton ?? self.shouldShowLogInButton,
        shouldShowDetective: shouldShowDetective ?? self.shouldShowDetective,
        showAsLoggedIn: showAsLoggedIn ?? self.showAsLoggedIn
      )
    }
  }
}

extension AccountView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case let ._showAsLoggedIn(value: value):
        if state.shouldShowLogInButton {
          state = state.update(
            shouldShowLogInButton: !value,
            showAsLoggedIn: value
          )
        }

      case ._didLogIn:
        state = state.update(shouldShowLogInButton: false)

      case ._didLogout:
        state = state.update(shouldShowLogInButton: !state.showAsLoggedIn)

      case let ._addNavigationEntries(navigationEntries):
        var newRows = state.rows
        newRows.append(contentsOf: navigationEntries.map { .navigationEntry($0) })
        state = state.update(rows: newRows)

      case let ._addStaticNavigationEntries(top: topEntries, bottom: bottomEntries):
        state = state.update(
          staticEntriesTop: topEntries.map { .navigationEntry($0) },
          staticEntriesBottom: bottomEntries.map { .navigationEntry($0) }
        )

      case let ._updateDetectiveVisibility(shouldShowDetective):
        state = state.update(shouldShowDetective: shouldShowDetective)

      case ._trackDidAppear, .didAppear, .didPressDetective, .didPressUrl:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let tracker: OGTrackerProtocol

    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.router = router
      self.tracker = tracker
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case let .didPressUrl(url):
        router.send(OGRoute(url: URL(string: url)))
        return nil

      case .didPressDetective:
        if state.shouldShowDetective {
          router.send(.detective)
        }
        return nil

      case ._addNavigationEntries, ._addStaticNavigationEntries, ._didLogIn,
           ._didLogout, ._showAsLoggedIn, ._updateDetectiveVisibility:
        return nil

      case .didAppear:
        return ._trackDidAppear

      case ._trackDidAppear:
        tracker.multiplatformTrack(event: ViewEvent.ScreenAccountMenu())
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    // swiftlint:disable nesting
    private struct AccountConfiguration: Equatable {
      let staticEntriesTop: [AccountNavigationEntry]
      let staticEntriesBottom: [AccountNavigationEntry]
      let showAsLoggedIn: Bool
    }

    private let account: AccountFeatureAdaptable
    private let accountStore: any OGDomainStoreViewStoreConsumable<AccountState>
    private let appEnvironment: OGAppEnvironmental
    private let appStore: any AppStoreProtocol
    private var cancellables = Set<AnyCancellable>()

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore(),
      accountStore: any OGDomainStoreViewStoreConsumable<AccountState> = AccountStoreContainer.shared.store(),
      account: AccountFeatureAdaptable = AccountFeatureAdapterContainer.shared.account(),
      appEnvironment: OGAppEnvironmental = OGCoreContainer.shared.appEnvironment()
    ) {
      self.appStore = appStore
      self.accountStore = accountStore
      self.account = account
      self.appEnvironment = appEnvironment
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await watchAppStore(dispatch: dispatch)
      await watchAccountStore(dispatch: dispatch)

      subscribeToAccountUpdates(dispatch: dispatch)
      await dispatch(._updateDetectiveVisibility(appEnvironment.isDebugOrBetaBuild))
    }

    func watchAppStore(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await appStore
        .watch(keyPath: \.userState)
        .filter { $0.isAwaitingUpdate == false }
        .removeDuplicates()
        .sink { userState in
          Task {
            await dispatch(userState.isLoggedIn ? ._didLogIn : ._didLogout)
          }
        }
        .store(in: &cancellables)
    }

    func watchAccountStore(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await accountStore
        .watch(keyPath: \.accountNavigationEntries)
        .removeDuplicates()
        .sink { accountNavigationEntries in
          Task {
            await dispatch(._addNavigationEntries(accountNavigationEntries))
          }
        }
        .store(in: &cancellables)
    }

    func subscribeToAccountUpdates(
      dispatch: @escaping (Event) async -> Void
    ) {
      account
        .configuration
        .map {
          AccountConfiguration(
            staticEntriesTop: $0.staticEntriesTop,
            staticEntriesBottom: $0.staticEntriesBottom,
            showAsLoggedIn: $0.showAsLoggedIn
          )
        }
        .removeDuplicates()
        .sink { configuration in
          Task {
            await dispatch(
              ._addStaticNavigationEntries(
                top: configuration.staticEntriesTop,
                bottom: configuration.staticEntriesBottom
              )
            )
          }
          Task {
            await dispatch(._showAsLoggedIn(value: configuration.showAsLoggedIn))
          }
        }
        .store(in: &cancellables)
    }
  }

  private struct AccountConfiguration: Equatable {
    let staticEntriesTop: [AccountNavigationEntry]
    let staticEntriesBottom: [AccountNavigationEntry]
    let showAsLoggedIn: Bool
  }
}
