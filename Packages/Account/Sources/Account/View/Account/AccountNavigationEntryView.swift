import OGL10n
import OGNavigationBar
import SwiftUI
import UICatalog

// MARK: - AccountNavigationEntryView

struct AccountNavigationEntryView: View {
  @StateObject private var viewStore: Self.Store

  public init(entry: AccountNavigationEntry) {
    _viewStore = StateObject(wrappedValue: Self.make(entry: entry))
  }

  var body: some View {
    makeNavigationEntry()
  }

  @ViewBuilder
  func makeNavigationEntry() -> some View {
    switch viewStore.entry.type {
    case .toggle:
      toggleView()
    case .section:
      sectionView()
    default:
      serviceView()
    }
  }

  @ViewBuilder
  func toggleView() -> some View {
    Toggle(isOn: .constant(false)) {
      titleLabel()
    }
    .padding(.trailing, 12)
  }

  @ViewBuilder
  func sectionView() -> some View {
    ZStack {
      VStack(spacing: .zero) {
        HStack {
          Text(viewStore.entry.localizedLabel)
            .font(for: .section)
            .textCase(.uppercase)
            .foregroundColor(OGColors.backgroundBackground100.color)
            .padding(UILayoutConstants.AccountView.headerTextPadding)
            .accessibilityAddTraits(.isHeader)
            .accessibilityLabel(
              "\(ogL10n.General.List.Items.Accessibility(amount: String(viewStore.entry.children?.count ?? 0))), \(viewStore.entry.localizedLabel)"
            )
          Spacer()
        }
        .frame(minHeight: UILayoutConstants.AccountView.sectionTitleHeight)

        ForEach(viewStore.entry.children ?? [], id: \.self) { child in
          AccountNavigationEntryView(entry: child)
          Divider()
            .background(OGColors.backgroundBackground20.color)
            .padding(UILayoutConstants.AccountView.dividerPadding)
        }
      }
    }
    .accessibilityElement(children: .contain)
    .padding(.top, UILayoutConstants.AccountView.topDefaultPadding.top)
  }

  @ViewBuilder
  func serviceView() -> some View {
    Button {
      Task {
        await viewStore.dispatch(.didTap)
      }
    } label: {
      titleLabel()
    }
    .buttonStyle(.plain)
  }

  @ViewBuilder
  func titleLabel() -> some View {
    HStack {
      Text(viewStore.entry.localizedLabel)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.backgroundBackground100.color)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(UILayoutConstants.AccountView.textPadding)
      Spacer()
      if viewStore.entry.type == .node || viewStore.entry.type == .section {
        OGImages.icon24x24ChevronRightPrimary.image
          .padding(UILayoutConstants.AccountView.accessoryPadding)
      }
    }
    .background(OGColors.backgroundBackground0.color)
    .frame(minHeight: UILayoutConstants.AccountView.minHeight)
  }
}

// MARK: - AccountNavigationEntryList

struct AccountNavigationEntryList: View {
  let entry: AccountNavigationEntry
  let title: String

  var body: some View {
    OGNavigationBar(titleView: Text(title), content: {
      content
    })
  }

  private var content: some View {
    ScrollView(.vertical, showsIndicators: false) {
      VStack(spacing: .zero) {
        ForEach(entry.children ?? [], id: \.self) { child in
          AccountNavigationEntryView(entry: child)
          Divider()
            .background(OGColors.backgroundBackground20.color)
            .padding(UILayoutConstants.AccountView.dividerPadding)
        }
      }
    }
  }
}
