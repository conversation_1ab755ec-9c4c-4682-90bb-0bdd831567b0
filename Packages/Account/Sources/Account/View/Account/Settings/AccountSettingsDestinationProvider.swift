import OGFeatureAdapter
import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - AccountSettingsDestinationProvider

public struct AccountSettingsDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.accountSettings
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      AccountSettingsView(title: title)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.Account.Settings.Title
  }
}

extension OGRoute {
  public static let accountSettings = OGRoute(OGIdentifier.accountSettings.value)
}

extension OGIdentifier {
  public static let accountSettings = #identifier("settings")
}
