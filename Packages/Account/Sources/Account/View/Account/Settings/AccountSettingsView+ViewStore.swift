import AppCore
import Combine
import Foundation
import OGAirshipKit
import OGAppEnvironment
import OGCore
import OGDomainStore
import OGL10n
import OGRouter
import OGViewStore
import UICatalog
import UIKit

// MARK: - AccountSettingsView.Store

extension AccountSettingsView {
  typealias Store = OGViewStore<ViewState, Event>
}

extension AccountSettingsView {
  static func make() -> Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - AccountSettingsView.Event

extension AccountSettingsView {
  enum Event: OGViewEvent, Hashable {
    /// Public Events
    case openSettings
    case copy

    case _numberOfTaps(Int)
    case _version(String)
    case _airshipChannelID(String)
  }
}

// MARK: - AccountSettingsView.ViewState

extension AccountSettingsView {
  struct ViewState: OGViewState {
    private(set) var numberOfTaps: Int = 0
    private(set) var version: String = ""
    private(set) var airshipChannelID: String = ""
    static var initial: AccountSettingsView.ViewState = .init()

    var settingsTitle: String {
      ogL10n.Account.Settings.Title
    }

    var appVersion: String {
      ogL10n.AppVersion
    }

    mutating func update(
      numberOfTaps: Int
    ) {
      self.numberOfTaps = numberOfTaps
    }

    mutating func update(
      version: String
    ) {
      self.version = version
    }

    mutating func update(
      airshipChannelID: String
    ) {
      self.airshipChannelID = airshipChannelID
    }
  }
}

extension AccountSettingsView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case .copy, .openSettings:
        break
      case let ._numberOfTaps(numberOfTaps):
        state.update(numberOfTaps: numberOfTaps)
      case let ._version(version):
        state.update(version: version)
      case let ._airshipChannelID(airshipChannelID):
        state.update(airshipChannelID: airshipChannelID)
      }
    }
  }

  final class Middleware: OGViewStoreMiddleware {
    private let application: ApplicationServing
    private var pasteboard: PasteboardServing
    init(
      application: ApplicationServing = UIApplication.shared,
      pasteboard: PasteboardServing = UIPasteboard.general
    ) {
      self.application = application
      self.pasteboard = pasteboard
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case .openSettings:
        Task { @MainActor in
          guard let openSettingsURL = application.openSettingsURL,
                application.canOpenURL(openSettingsURL) else { return }
          application.open(url: openSettingsURL)
        }
        return nil
      case .copy:
        pasteboard.string = state.airshipChannelID
        return nil
      case ._airshipChannelID, ._numberOfTaps, ._version:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables: Set<AnyCancellable> = []
    private let airshipChannelIdDialog: OGAirshipChannelIdDialogFeatureAdaptable?
    private let environment: OGAppEnvironmental

    init(
      airshipChannelIdDialog: OGAirshipChannelIdDialogFeatureAdaptable? = OGAirshipContainer.shared.channelID(),
      environment: OGAppEnvironmental = OGCoreContainer.shared.appEnvironment()
    ) {
      self.airshipChannelIdDialog = airshipChannelIdDialog
      self.environment = environment
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      airshipChannelIdDialog?
        .configuration
        .sink { configuration in
          Task {
            await dispatch(
              ._numberOfTaps(configuration.numberOfTaps)
            )
            await dispatch(
              ._airshipChannelID(configuration.channelID ?? "NAN")
            )
          }
        }
        .store(in: &cancellables)
      if let version = environment.version, let buildNumber = environment.buildNumber {
        await dispatch(
          ._version("\(version) (\(buildNumber))")
        )
      }
    }
  }
}

// MARK: - ApplicationServing

public protocol ApplicationServing {
  var openSettingsURL: URL? { get }
  func canOpenURL(_ url: URL) -> Bool
  func open(url: URL)
}

// MARK: - UIApplication + ApplicationServing

extension UIApplication: ApplicationServing {
  public func open(url: URL) {
    open(url)
  }

  public var openSettingsURL: URL? {
    URL(string: UIApplication.openSettingsURLString)
  }
}
