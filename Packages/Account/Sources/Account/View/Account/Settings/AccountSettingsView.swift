import AppCore
import OGNavigationBar
import SwiftUI
import UICatalog

// MARK: - AccountSettingsView

struct AccountSettingsView: View {
  @StateObject private var viewStore: Self.Store
  @State private var isPresented = false

  private let title: String

  public init(title: String) {
    _viewStore = StateObject(wrappedValue: Self.make())
    self.title = title
  }

  var body: some View {
    OGNavigationBar(titleView: Text(title), content: {
      content
    })
  }

  var content: some View {
    ScrollView(.vertical, showsIndicators: false) {
      VStack(spacing: .zero) {
        version
        divider

        settings
        divider

        MotionSettingToggle()
          .padding(.horizontal, UILayoutConstants.Default.padding2x)
        divider
      }
    }
    .alert("Airship", isPresented: $isPresented) {
      Button("Copy", role: .cancel) {
        Task {
          await viewStore.dispatch(.copy)
        }
      }
    } message: {
      Text("Channel ID: \(viewStore.airshipChannelID)")
    }
  }

  @ViewBuilder var divider: some View {
    Divider()
      .background(OGColors.backgroundBackground20.color)
      .padding(UILayoutConstants.AccountView.dividerPadding)
  }

  @ViewBuilder var settings: some View {
    HStack {
      Text(viewStore.settingsTitle)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.backgroundBackground100.color)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(UILayoutConstants.AccountSettingsView.textPadding)
      Spacer()
      OGImages.icon24x24ChevronRightPrimary.image
        .padding(UILayoutConstants.AccountSettingsView.accessoryPadding)
    }
    .background(OGColors.backgroundBackground0.color)
    .onTapGesture {
      Task {
        await viewStore.dispatch(.openSettings)
      }
    }
  }

  @ViewBuilder var version: some View {
    HStack {
      Text("\(viewStore.appVersion): \(viewStore.version)")
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.backgroundBackground100.color)
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(UILayoutConstants.AccountSettingsView.textPadding)
      Spacer()
    }
    .background(OGColors.backgroundBackground0.color)
    .onTapGesture(count: viewStore.numberOfTaps, perform: {
      isPresented.toggle()
    })
  }
}

// MARK: - UILayoutConstants.AccountSettingsView

extension UILayoutConstants {
  enum AccountSettingsView {
    static let textPadding = EdgeInsets(
      top: 11.0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 11.0,
      trailing: UILayoutConstants.Default.padding
    )
    static let accessoryPadding = EdgeInsets(
      top: 10.0,
      leading: 0,
      bottom: 10.0,
      trailing: 14.0
    )
    static let dividerPadding = EdgeInsets(
      top: 0,
      leading: UILayoutConstants.Default.padding2x,
      bottom: 0,
      trailing: 0
    )
  }
}
