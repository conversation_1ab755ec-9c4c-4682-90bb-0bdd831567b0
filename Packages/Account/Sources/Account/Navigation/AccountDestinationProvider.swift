import OGFeatureAdapter
import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - AccountDestinationProvider

public struct AccountDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.account
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      AccountView(title: title)
    }
  }

  public func presentationType() -> OGPresentationType {
    .replace
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.Account.Title
  }
}

extension OGRoute {
  public static let account = OGRoute(OGIdentifier.account.value)
  public static let accountDetail = OGRoute(OGIdentifier.accountDetail.value)

  enum CustomerAccount {
    public static func make(
      accountFeatureAdapter: AccountFeatureAdaptable,
      baseUrl: OGBaseUrlFeatureAdaptable
    ) -> OGRoute {
      let accountUrl = accountFeatureAdapter.configuration.value.accountUrl
      let url = baseUrl.urlRelativeToWebUrl(forUrlPath: accountUrl)
      return OGRoute(url: url)
    }
  }

  enum Logout {
    public static func make(
      accountFeatureAdapter: AccountFeatureAdaptable,
      baseUrl: OGBaseUrlFeatureAdaptable
    ) -> OGRoute {
      let logoutUrl = accountFeatureAdapter.configuration.value.logoutUrl
      let url = baseUrl.urlRelativeToWebUrl(forUrlPath: logoutUrl)
      return OGRoute(url: url)
    }
  }
}

// MARK: - AccountDetailDestinationProvider

public struct AccountDetailDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.accountDetail
  }

  public func provide(_ route: OGRoute) -> some View {
    if let entry: AccountNavigationEntry = route.getData() {
      viewWithTitle(for: route) { title in
        AccountNavigationEntryList(entry: entry, title: title)
      }
    }
  }

  public func title(for route: OGRoute?) -> String? {
    let entry: AccountNavigationEntry? = route?.getData()
    return entry?.localizedLabel
  }
}

// MARK: - OGRoute.Login

extension OGRoute {
  enum Login {
    public static func make(
      accountFeatureAdapter: AccountFeatureAdaptable = AccountFeatureAdapterContainer.shared.account()
    ) -> OGRoute {
      let loginUrl = accountFeatureAdapter.configuration.value.loginUrl
      let url = URL(string: loginUrl)
      return OGRoute(url: url)
    }
  }
}

extension OGNavigationItem {
  public static let login = OGNavigationItem(route: .Login.make())
}
