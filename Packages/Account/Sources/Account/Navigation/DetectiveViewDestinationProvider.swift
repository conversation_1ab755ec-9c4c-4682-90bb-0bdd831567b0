import OGCore
import OGDetective
import OGFeatureAdapter
import OGMacros
import OGNavigation
import SwiftUI

extension OGIdentifier {
  public static let detective = #identifier("detective")
}

extension OGRoute {
  public static let detective = OGRoute(OGIdentifier.detective.value)
}

// MARK: - DetectiveViewDestinationProvider

public struct DetectiveViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  private var appEnvironment: OGAppEnvironmental

  public init(appEnvironment: OGAppEnvironmental = OGCoreContainer.shared.appEnvironment()) {
    self.identifier = .detective
    self.appEnvironment = appEnvironment
  }

  public func provide(_: OGRoute) -> some View {
    if appEnvironment.isDebugOrBetaBuild {
      viewWithTitle { title in
        OGDetectiveView(title: title)
      }
    }
  }

  public func presentationType() -> OGPresentationType {
    .fullScreenCover
  }

  public func title(for route: OGRoute?) -> String? {
    "Detective"
  }
}
