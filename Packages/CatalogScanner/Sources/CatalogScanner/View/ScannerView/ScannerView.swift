import OGL10n

import OGNavigationBar
import SwiftUI
import UICatalog

// MARK: - ScannerView

public struct ScannerView: View {
  @StateObject private var viewStore: Self.Store

  private let title: String

  public init(title: String) {
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    OGNavigationBar(titleView: Text(title)) {
      CSARSceneView()
        .edgesIgnoringSafeArea(.all)
        .onAppear {
          Task {
            await viewStore.dispatch(.didAppear)
          }
        }
    }
  }
}

// MARK: - UILayoutConstants.ScannerView

extension UILayoutConstants {
  enum ScannerView {
    static let opacity = 0.8
  }
}

#Preview {
  ScannerView(title: "Title")
}
