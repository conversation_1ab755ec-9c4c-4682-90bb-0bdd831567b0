import Foundation
import OGIdentifier
import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - ScannerViewDestinationProvider

public struct ScannerViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .scannerView

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      ScannerView(title: title)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.CatalogScanner.Onboarding.ScanPage.Title
  }
}

extension OGIdentifier {
  static let scannerView = #identifier("scannerView")
}

extension OGRoute {
  static let scannerView = OGRoute(OGIdentifier.scannerView.value)
}
