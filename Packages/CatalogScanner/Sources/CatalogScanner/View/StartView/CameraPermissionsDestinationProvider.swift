import OGMacros
import OGNavigation
import SwiftUI

extension OGIdentifier {
  static let checkCameraPermission = #identifier("checkCameraPermission")
}

extension OGRoute {
  static let checkCameraPermission = OGRoute(OGIdentifier.checkCameraPermission.value)
}

// MARK: - CameraPermissionsDestinationProvider

public struct CameraPermissionsDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .checkCameraPermission

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    CheckCameraPermissionsView()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
