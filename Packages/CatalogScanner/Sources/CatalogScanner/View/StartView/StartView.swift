import AppCore
import OGL10n

import OGNavigationBar
import SwiftUI
import UICatalog

// MARK: - StartView

struct StartView: View {
  @EnvironmentObject private var deviceState: DeviceState
  @StateObject private var viewStore: Self.Store
  private let buttonStyleResolver: ButtonStyleFactory
  private let title: String

  public init(
    title: String,
    buttonStyleResolver: ButtonStyleFactory = CatalogScannerContainer.shared.buttonStyleResolver()
  ) {
    _viewStore = StateObject(wrappedValue: Self.make())
    self.title = title
    self.buttonStyleResolver = buttonStyleResolver
  }

  var body: some View {
    OGNavigationBar(titleView: Text(title)) {
      ScrollView {
        content
      }.basedOnSizeScrollBehavior()
    }
  }

  private var content: some View {
    VStack(spacing: UILayoutConstants.CatalogScannerStartView.spacing) {
      switch viewStore.loadableState {
      case .error:
        errorView
      case .loaded, .loading:
        imageAndTextView
        buttonView
      }
    }
    .padding(.vertical, UILayoutConstants.Default.padding3x)
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear)
      }
    }
  }

  private var errorView: some View {
    ErrorView(type: .defaultError) {
      Task {
        await viewStore.dispatch(.fetch)
      }
    }
  }

  private var bottomCover: some View {
    HStack {
      imageWithOverlay(
        tilt: -UILayoutConstants.CatalogScannerStartView.imageTiltRadius,
        imageIndex: 0
      )
      Spacer()
        .frame(width: UILayoutConstants.CatalogScannerStartView.imageSpacerWidth)
    }
  }

  private var topCover: some View {
    HStack {
      Spacer()
        .frame(width: UILayoutConstants.CatalogScannerStartView.imageSpacerWidth)
      imageWithOverlay(
        tilt: UILayoutConstants.CatalogScannerStartView.imageTiltRadius,
        imageIndex: 1
      )
    }
  }

  private func imageWithOverlay(tilt: Double, imageIndex: Int) -> some View {
    func shouldShowOverlay() -> Bool {
      if case let .loaded(images) = viewStore.loadableState,
         let images,
         images.count < 2 {
        true
      } else {
        false
      }
    }
    return OGImages.catalogScannerCatalog.image
      .accessibilityHidden(true)
      .opacity(shouldShowOverlay() ? .zero : 1)
      .rotationEffect(Angle(degrees: tilt))
      .shadow(radius: UILayoutConstants.CatalogScannerStartView.imageShadowRadius)
      .overlay {
        if case let .loaded(images) = viewStore.loadableState, let image = images?[safe: imageIndex] {
          image
            .resizable()
            .accessibilityHidden(true)
            .scaledToFit()
            .rotationEffect(Angle(degrees: tilt))
            .shadow(radius: UILayoutConstants.CatalogScannerStartView.imageShadowRadius)
        }
      }
  }

  private var coverImages: some View {
    ZStack(alignment: .center) {
      bottomCover
      topCover
    }
    .padding(UILayoutConstants.Default.padding3x)
    .accessibilityAddTraits(.isImage)
  }

  private var imageAndTextView: some View {
    VStack(spacing: UILayoutConstants.Default.padding2x) {
      coverImages
        .animation(
          .easeInOut(duration: UILayoutConstants.CatalogScannerStartView.animationDuration),
          value: viewStore.loadableState != .loading
        )
      Text(ogL10n.CatalogScanner.Onboarding.Title)
        .font(for: .headlineLEmphasized)
        .foregroundColor(OGColors.textOnLight.color)
        .accessibilityAddTraits(.isHeader)
      Text(ogL10n.CatalogScanner.Onboarding.Text)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
    }
  }

  private var buttonView: some View {
    VStack(spacing: UILayoutConstants.Default.padding) {
      if viewStore.isARWorldTrackingSupported {
        ZStack {
          C2AButton(
            title: viewStore.openScannerButtonTittle,
            accessibilityIdentifier: ogL10n.CatalogScanner.Onboarding.ScanButton
          ) {
            Task {
              await viewStore.dispatch(.requestPermission)
            }
          }
          .register(buttonStyleResolver.primaryButtonStyle)
          .disabled(viewStore.isOpenScannerButtonDisabled)
          .opacity(viewStore.openScannerButtonOpacity)
          if viewStore.isLoading {
            ProgressView()
              .progressViewStyle(CircularProgressViewStyle(tint: OGColors.backgroundBackground0.color))
          }
        }
      }
      C2AButton(
        title: ogL10n.CatalogScanner.Onboarding.Orderbutton,
        accessibilityIdentifier: ogL10n.CatalogScanner.Onboarding.Orderbutton
      ) {
        Task {
          await viewStore.dispatch(.orderCatalog)
        }
      }
      .register(buttonStyleResolver.secondaryButtonStyle)
    }
  }
}

#Preview {
  StartView(title: "Title")
}

// MARK: - UILayoutConstants.CatalogScannerStartView

extension UILayoutConstants {
  enum CatalogScannerStartView {
    static let imageShadowRadius: CGFloat = 5.0
    static let imageTiltRadius: CGFloat = 6.0
    static let imageSpacerWidth: CGFloat = 60
    static let animationDuration: CGFloat = 0.3
    static let spacing: CGFloat = 40
  }
}
