import Foundation
import OGL10n
import OGNavigation
import SwiftUI

// MARK: - CatalogScannerDestinationProvider

public struct CatalogScannerDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .catalogScanner

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      StartView(title: title)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.CatalogScanner.Title
  }
}

extension OGRoute {
  public static let catalogScanner = OGRoute(OGIdentifier.catalogScanner.value)
}
