import AVKit
import <PERSON>GAsyncImage
import SwiftUI
import UICatalog

// MARK: - CSVideoButton

struct CSVideoButton: View {
  @StateObject private var viewStore: Self.Store
  private let buttonStyleResolver: ButtonStyleFactory

  public init(
    video: Video,
    buttonStyleResolver: ButtonStyleFactory = CatalogScannerContainer.shared.buttonStyleResolver()
  ) {
    _viewStore = StateObject(wrappedValue: Self.make(video: video))
    self.buttonStyleResolver = buttonStyleResolver
  }

  var body: some View {
    VStack(spacing: 0) {
      if viewStore.video != nil {
        videoContainer
      }

      Text(viewStore.video?.title ?? String())
        .font(for: .headlineLEmphasized)
        .foregroundColor(OGColors.textOnLight.color)
        .accessibilityAddTraits(.isHeader)
        .multilineTextAlignment(.center)
        .padding(.top, UILayoutConstants.Default.padding2x)

      Text(viewStore.video?.subTitle ?? String())
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.center)
        .padding(.top, UILayoutConstants.Default.paddingHalf)

      Button(action: {
        Task {
          await viewStore.dispatch(.didTapPlayButton)
        }
      }) {
        Text(viewStore.video?.buttonLabel ?? String())
          .font(for: OGFonts.buttonLabelM)
          .foregroundColor(.white)
          .padding(.horizontal, UILayoutConstants.Default.padding3x)
          .padding(.vertical, UILayoutConstants.Default.padding)
          .background(
            RoundedRectangle(cornerRadius: UILayoutConstants.CSVideoButton.buttonCornerRadius)
              .fill(OGColors.primaryPrimary100.color)
          )
      }
      .padding(.top, UILayoutConstants.Default.padding2x)
      .accessibilityIdentifier(viewStore.video?.buttonLabel ?? String())
      .padding(.bottom)
    }
    .background(OGColors.backgroundBackground10.color)
  }

  @ViewBuilder private var videoContainer: some View {
    ZStack {
      if viewStore.isVideoPlaying, viewStore.isReadyToPlay {
        VideoPlayerView(
          player: viewStore.player,
          videoTitle: viewStore.videoTitle,
          allowsPictureInPicturePlayback: true,
          showsPlaybackControls: true,
          accessibilityLabel: viewStore.video?.altText
        )
      } else {
        ZStack {
          OGAsyncImage(url: viewStore.video?.thumbnailImageUrl, contentMode: .fill)
            .clipped()
            .accessibilityLabel("\(viewStore.video?.title ?? "")")

          Button(action: {
            Task {
              await viewStore.dispatch(.didTapPlayButton)
            }
          }) {
            ZStack {
              Circle()
                .fill(OGColors.backgroundBackground0.color)
                .frame(width: UILayoutConstants.CSVideoButton.playButtonBackgroundSize, height: UILayoutConstants.CSVideoButton.playButtonBackgroundSize)

              OGImages.icon24x24Play.image
                .foregroundColor(OGColors.backgroundBackground0.color)
            }
          }
        }
      }
    }
    .frame(height: UILayoutConstants.CSVideoButton.videoFrameHeight)
  }
}

#Preview {
  CSVideoButton(
    video: Video(
      title: "Spring-/summer campaign 2025",
      subTitle: "Purple. Lieben Wir.",
      thumbnailImageUrl: nil,
      videoUrl: nil,
      buttonLabel: "Klick here",
      buttonLink: nil,
      altText: nil
    ))
}

// MARK: - UILayoutConstants.CSVideoButton

extension UILayoutConstants {
  enum CSVideoButton {
    static let videoFrameHeight: CGFloat = 192
    static let videoCornerRadius: CGFloat = 8.0
    static let buttonCornerRadius: CGFloat = 26.0
    static let playButtonBackgroundSize: CGFloat = 60
  }
}
