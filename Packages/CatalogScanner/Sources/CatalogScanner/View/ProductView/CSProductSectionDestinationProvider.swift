import OGMacros
import OGNavigation
import SwiftUI

// MARK: - CSProductSectionDestinationProvider

public struct CSProductSectionDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .productSelection

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    CSProductSectionView()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

// MARK: - OGIdentifier Extension

extension OGIdentifier {
  public static let productSelection = #identifier("productSelection")
}

// MARK: - OGRoute Extension

extension OGRoute {
  public static let productSelectionView = OGRoute(OGIdentifier.productSelection.value)
}
