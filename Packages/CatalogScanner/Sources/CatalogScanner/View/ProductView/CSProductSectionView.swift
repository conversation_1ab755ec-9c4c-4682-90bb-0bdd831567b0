import AppCore
import OGL10n

import SwiftUI
import UICatalog

// MARK: - CSProductSectionView

struct CSProductSectionView: View {
  @StateObject private var viewStore: Self.Store
  @EnvironmentObject private var deviceState: DeviceState

  init(viewStore: Self.Store? = nil) {
    let store = viewStore ?? Self.make()
    _viewStore = StateObject(wrappedValue: store)
  }

  var columns: [GridItem] {
    let spacing = UILayoutConstants.CSProductSectionView.gridViewSpacing

    if deviceState.isLandscapeOrientation {
      return [
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .leading, vertical: .top)),
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .leading, vertical: .top)),
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .leading, vertical: .top)),
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .trailing, vertical: .top))
      ]
    } else {
      return [
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .leading, vertical: .top)),
        GridItem(.flexible(), spacing: spacing, alignment: .init(horizontal: .trailing, vertical: .top))
      ]
    }
  }

  var body: some View {
    VStack(alignment: .leading, spacing: 0) {
      ScrollView {
        VStack(spacing: 0) {
          videoHeader
            .padding(.horizontal, UILayoutConstants.Default.padding2x)

          productView
            .padding(.horizontal, UILayoutConstants.Default.padding2x)

          Spacer()
            .frame(height: UILayoutConstants.CSProductSectionView.additionalScrollSpace)
        }
        .padding(.top, UILayoutConstants.Default.padding2x)
      }
    }
    .navigationTitle(ogL10n.CatalogScanner.Products.Title(catalogPage: viewStore.state.pageName))
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear)
      }
    }
  }

  @ViewBuilder private var videoHeader: some View {
    if let video = viewStore.video {
      CSVideoButton(video: video)
        .padding(.bottom, UILayoutConstants.Default.padding3x)
    }
  }

  private var productView: some View {
    LazyVGrid(
      columns: columns,
      spacing: UILayoutConstants.CSProductSectionView.gridViewSpacing,
      pinnedViews: [.sectionHeaders]
    ) {
      ForEach(viewStore.products, id: \.self) { product in
        productCell(for: product)
      }
    }
  }

  private func productCell(for product: Product) -> some View {
    CSProductCell(
      productImageUrl: product.imageUrl,
      title: product.title,
      productDescription: product.description,
      variantsImageUrls: product.variantPreviewImageUrls
    ) {
      Task {
        await viewStore.dispatch(.didTapProduct(product: product))
      }
    }
  }
}

// MARK: - UILayoutConstants.CSProductSectionView

extension UILayoutConstants {
  enum CSProductSectionView {
    static let gridViewSpacing: CGFloat = 20.0
    static let additionalScrollSpace: CGFloat = 64.0
    static let handleWidth: CGFloat = 36.0
    static let handleHeight: CGFloat = 4.0
    static let handleCornerRadius: CGFloat = 2.5
  }
}
