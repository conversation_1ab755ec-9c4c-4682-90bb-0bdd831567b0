import Foundation

public struct Video: Codable, Hashable, Equatable, Sendable {
  let title: String?
  let subTitle: String?
  let thumbnailImageUrl: URL?
  let videoUrl: URL?
  let buttonLabel: String?
  let buttonLink: URL?
  let altText: String?

  public init(title: String?, subTitle: String?, thumbnailImageUrl: URL?, videoUrl: URL?, buttonLabel: String?, buttonLink: URL?, altText: String?) {
    self.title = title
    self.subTitle = subTitle
    self.thumbnailImageUrl = thumbnailImageUrl
    self.videoUrl = videoUrl
    self.buttonLabel = buttonLabel
    self.buttonLink = buttonLink
    self.altText = altText
  }
}
