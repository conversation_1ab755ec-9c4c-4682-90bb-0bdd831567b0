import CatalogScannerTestsUtils
import OGRouter
import OGRouterTestsUtils
import OGTracker
import OGTrackerTestsUtils
import XCTest

@testable import CatalogScanner

final class CSProductSectionViewMiddlewareTests: XCTestCase {
  func test_WHEN_didClickProduct_THEN_sendRoute() async throws {
    let routerMock = OGRoutePublisherMock()

    let product: Product = .stub

    let state = CSProductSectionView.ViewState.initial
    let nextAction = await CSProductSectionView.Middleware(router: routerMock).callAsFunction(event: .didTapProduct(product: product), for: state)

    XCTAssertEqual(routerMock.mock.sendCalls.callsCount, 1)
    XCTAssertEqual(routerMock.mock.sendCalls.latestCall, .some(OGRoute(url: .stub)))
    XCTAssertEqual(nextAction, ._trackDidTapProduct)
  }

  func test_WHEN_trackDidTapProduct_THEN_noNextAction() async throws {
    let sut = CSProductSectionView.Middleware()
    let nextAction = await sut.callAsFunction(
      event: ._trackDidTapProduct,
      for: .initial
    )

    XCTAssertNil(nextAction)
  }

  func test_WHEN_trackDidTapProduct_THEN_CatalogScannerSelectProduct_eventTracked() async throws {
    let tracker = OGTrackerMock()
    let sut = CSProductSectionView.Middleware(tracker: tracker)
    _ = await sut.callAsFunction(
      event: ._trackDidTapProduct,
      for: .initial
    )

    let actualEvent = tracker.mock.multiplatformTrackCalls.latestCall as? InteractionEvent.CatalogScannerSelectProduct
    let expectedEvent = InteractionEvent.CatalogScannerSelectProduct()

    XCTAssertEqual(actualEvent, expectedEvent)
    XCTAssertEqual(tracker.mock.multiplatformTrackCalls.callsCount, 1)
  }

  func test_WHEN_updateCurrentProducts_THEN_returnNil() async throws {
    let routerMock = OGRoutePublisherMock()
    let state = CSProductSectionView.ViewState.initial
    let result = await CSProductSectionView.Middleware(router: routerMock).callAsFunction(event: ._updatePage(name: "", products: [.stub], video: nil), for: state)

    XCTAssertNil(result)
  }

  func test_WHEN_updatePageName_THEN_returnNil() async throws {
    let routerMock = OGRoutePublisherMock()

    let state = CSProductSectionView.ViewState.initial
    let result = await CSProductSectionView.Middleware(router: routerMock).callAsFunction(event: ._updatePage(name: "New Page", products: [], video: nil), for: state)

    XCTAssertNil(result)
  }

  func test_WHEN_updateVideo_THEN_returnNil() async throws {
    let routerMock = OGRoutePublisherMock()
    let video = Video(
      title: nil,
      subTitle: nil,
      thumbnailImageUrl: nil,
      videoUrl: nil,
      buttonLabel: nil,
      buttonLink: URL(string: "www.otto.de"),
      altText: nil
    )
    let state = CSProductSectionView.ViewState.initial

    let result = await CSProductSectionView.Middleware(router: routerMock).callAsFunction(event: ._updatePage(name: "", products: [], video: video), for: state)

    XCTAssertNil(result)
  }

  func test_WHEN_didAppear_THEN_trackDidAppear() async throws {
    let sut = CSProductSectionView.Middleware()
    let nextAction = await sut.callAsFunction(
      event: .didAppear,
      for: .initial
    )

    XCTAssertEqual(nextAction, ._trackDidAppear)
  }

  func test_WHEN_trackDidAppear_THEN_OverlayCatalogScannerProductOverlay_eventTracked() async throws {
    let tracker = OGTrackerMock()
    let sut = CSProductSectionView.Middleware(tracker: tracker)
    let nextAction = await sut.callAsFunction(
      event: ._trackDidAppear,
      for: .initial
    )

    XCTAssertNil(nextAction)

    let latestEvent = tracker.mock.multiplatformTrackCalls.latestCall as! ViewEvent.OverlayCatalogScannerProductOverlay
    let expectedEvent = ViewEvent.OverlayCatalogScannerProductOverlay()
    XCTAssertEqual(latestEvent, expectedEvent)
    XCTAssertEqual(tracker.mock.multiplatformTrackCalls.callsCount, 1)
  }
}
