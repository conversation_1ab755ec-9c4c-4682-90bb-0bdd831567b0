import Combine
import CoreLocation
import MapKit
import OGCoreTestsUtils
import OGTracker
import OGTrackerTestsUtils
import StoreFinderTestsUtils
import XCTest

@testable import StoreFinder

final class DetailStoreViewMiddlewareTests: XCTestCase {
  func test_WHEN_loadMapImage_THEN_mapImageLoadedEventIsReturned() async throws {
    let mockSnapshotImage = UIImage(systemName: "star.fill")!
    let mapServiceMock = MapServiceMock()

    mapServiceMock.mock.loadMapImageCalls.mockCall { _ in
      mockSnapshotImage
    }

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState()

    let event = await sut.callAsFunction(event: .loadMapImage, for: state)

    if case let .mapImageLoaded(image) = event {
      XCTAssertEqual(image, mockSnapshotImage)
    } else {
      XCTFail("Expected .mapImageLoaded event")
    }
  }

  func test_WHEN_openInGoogleMaps_THEN_call_ServiceOpenInGoogleMaps() async throws {
    let mapServiceMock = MapServiceMock()

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState()

    let event = await sut.callAsFunction(event: .openInGoogleMaps, for: state)

    XCTAssertEqual(mapServiceMock.mock.openInGoogleMapsCalls.callsCount, 1)
    XCTAssertNil(event)
  }

  func test_WHEN_checkForGoogleMaps_IfTrue_THEN_googleMapsIsInstalled() async throws {
    let mapServiceMock = MapServiceMock()

    mapServiceMock.mock.isGoogleMapsInstalledCalls.mockCall { _ in
      true
    }

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState()

    let event = await sut.callAsFunction(event: ._checkForGoogleMaps, for: state)

    XCTAssertEqual(event, ._googleMapsIsInstalled)
  }

  func test_WHEN_checkForGoogleMaps_IfFalse_THEN_nil() async throws {
    let mapServiceMock = MapServiceMock()

    mapServiceMock.mock.isGoogleMapsInstalledCalls.mockCall { _ in
      false
    }

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState()

    let event = await sut.callAsFunction(event: ._checkForGoogleMaps, for: state)

    XCTAssertNil(event)
  }

  func test_WHEN_openInMaps_THEN_noEventIsReturned() async throws {
    let mapServiceMock = MapServiceMock()
    var openInMapsCalled = false
    mapServiceMock.mock.openInMapsCalls.mockCall { _ in
      openInMapsCalled = true
    }

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState.initial

    let event = await sut.callAsFunction(event: .openInMaps, for: state)

    XCTAssertTrue(openInMapsCalled)
    XCTAssertNil(event)
  }

  func test_WHEN_callNumber_THEN_noEventIsReturned() async throws {
    let mapServiceMock = MapServiceMock()
    var callNumberCalled = false

    mapServiceMock.mock.callNumberCalls.mockCall { _ in
      callNumberCalled = true
    }

    let sut = DetailStoreView.Middleware(mapService: mapServiceMock)
    let state = DetailStoreView.ViewState.initial

    let event = await sut.callAsFunction(event: .callNumber("1234567890"), for: state)

    XCTAssertTrue(callNumberCalled)
    XCTAssertNil(event)
  }

  func test_WHEN_didAppear_THEN_trackDidAppear() async throws {
    let sut = DetailStoreView.Middleware()
    let event = await sut.callAsFunction(event: .didAppear, for: .initial)

    XCTAssertEqual(event, ._trackDidAppear)
  }

  func test_WHEN_trackDidAppear_THEN_ScreenStorefinderStoreDetail_tracked() async throws {
    let tracker = OGTrackerMock()
    let storeFinderTracker = StoreFinderTracker(tracker: tracker)
    let sut = DetailStoreView.Middleware(tracker: storeFinderTracker)
    let nextAction = await sut.callAsFunction(event: ._trackDidAppear, for: .initial)

    XCTAssertNil(nextAction)

    let latestEvent = tracker.mock.multiplatformTrackCalls.latestCall as? ViewEvent.ScreenStorefinderStoreDetail
    XCTAssertEqual(latestEvent, ViewEvent.ScreenStorefinderStoreDetail())
    XCTAssertEqual(tracker.mock.multiplatformTrackCalls.callsCount, 1)
  }
}
