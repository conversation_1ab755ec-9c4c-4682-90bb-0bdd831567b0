import Combine
import CoreLocation
import OGCoreTestsUtils
import StoreFinderTestsUtils
import XCTest

@testable import StoreFinder

final class DetailStoreViewConnectorTests: XCTestCase {
  func test_WHEN_configure_THEN_dispatchEvents() async {
    let expectation = expectation(description: "Expected events to be dispatched")
    let testStore = StoreFinder.Store.stub
    let sut = DetailStoreView.Connector(store: testStore)

    var actualEvents: [DetailStoreView.Event] = []
    let dispatch = { event in
      actualEvents.append(event)
      if actualEvents.count == 2 {
        expectation.fulfill()
      }
    }

    await sut.configure(dispatch: dispatch)
    await fulfillment(of: [expectation], timeout: 0.1)

    XCTAssertEqual(actualEvents, [._fetchStore(testStore), .loadMapImage, ._checkForGoogleMaps])
  }
}
