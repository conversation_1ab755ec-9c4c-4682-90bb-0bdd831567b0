import OGMacros
import OGMock
import <PERSON><PERSON>inder
import UIKit

@OGMock
public class MapServiceMock: MapServicing {
  public init() {}

  public func loadMapImage(for store: StoreFinder.Store) async -> UIImage? {
    await mock.loadMapImage(for: store)
  }

  public func openInMaps(for store: StoreFinder.Store) {
    mock.openInMaps(for: store)
  }

  public func callNumber(_ number: String) {
    mock.callNumber(number)
  }

  public func isGoogleMapsInstalled() -> Bool {
    mock.isGoogleMapsInstalled()
  }

  public func openInGoogleMaps(for store: StoreFinder.Store) {
    mock.openInGoogleMaps(for: store)
  }
}
