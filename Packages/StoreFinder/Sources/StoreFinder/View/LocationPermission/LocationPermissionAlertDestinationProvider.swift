import OGMacros
import OGNavigation
import SwiftUI

extension OGIdentifier {
  static let locationPermissionAlert = #identifier("locationPermissionAlert")
}

extension OGRoute {
  static let locationPermissionAlert = OGRoute(OGIdentifier.locationPermissionAlert.value)
}

// MARK: - LocationPermissionAlertDestinationProvider

public struct LocationPermissionAlertDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .locationPermissionAlert

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    LocationPermissionAlert()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
