import Combine
import MapKit
import OGCore
import OGL10n
import OGViewStore
import SwiftUI

// MARK: - DetailStoreView.Store

extension DetailStoreView {
  typealias Store = OGViewStore<ViewState, Event>
}

extension DetailStoreView {
  @MainActor
  static func make(store: StoreFinder.Store) -> DetailStoreView.Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector(store: store)
    )
  }
}

// MARK: - DetailStoreView.ViewState

extension DetailStoreView {
  struct ViewState: OGViewState {
    private(set) var mapImage: UIImage?
    private(set) var showMapAlert: Bool = false
    private(set) var hasGoogleMapsInstalled = false
    private(set) var store = StoreFinder.Store()
    private var appName: String?

    public static var initial: Self {
      .init()
    }

    public init(appEnvironment: any OGAppEnvironmental = OGCoreContainer.shared.appEnvironment()) {
      self.appName = appEnvironment.appName
    }

    mutating func update(
      mapImage: UIImage? = nil,
      showMapAlert: Bool? = nil,
      hasGoogleMapsInstalled: Bool? = nil,
      store: StoreFinder.Store? = nil
    ) {
      self.mapImage = mapImage ?? self.mapImage
      self.showMapAlert = showMapAlert ?? self.showMapAlert
      self.store = store ?? self.store
      self.hasGoogleMapsInstalled = hasGoogleMapsInstalled ?? self.hasGoogleMapsInstalled
    }

    public var isInternal: Bool {
      store.type == .internalType
    }

    public var storeTypeText: String? {
      guard let appName else { return nil }
      return store.type == .partner
        ? ogL10n.ShopFinder.Details.StoreType.Partner
        : ogL10n.ShopFinder.Details.StoreType.Company(company: appName)
    }
  }
}

// MARK: - DetailStoreView.Event

extension DetailStoreView {
  enum Event: OGViewEvent {
    case loadMapImage
    case mapImageLoaded(UIImage?)
    case showMapAlert(Bool)
    case openInMaps
    case callNumber(String)
    case didAppear
    case openInGoogleMaps
    /// Private
    case _fetchStore(StoreFinder.Store)
    case _trackDidAppear
    case _checkForGoogleMaps
    case _googleMapsIsInstalled
  }
}

// MARK: - DetailStoreView.Reducer

extension DetailStoreView {
  enum Reducer: OGViewEventReducible {
    static func reduce(_ state: inout ViewState, with event: Event) {
      switch event {
      case .loadMapImage:
        state.update(mapImage: nil)
      case let .mapImageLoaded(image):
        state.update(mapImage: image)
      case let .showMapAlert(show):
        state.update(showMapAlert: show)
      case let ._fetchStore(store):
        state.update(store: store)
      case ._googleMapsIsInstalled:
        state.update(hasGoogleMapsInstalled: true)
      case ._checkForGoogleMaps, ._trackDidAppear, .callNumber, .didAppear, .openInGoogleMaps, .openInMaps:  break
      }
    }
  }
}

// MARK: - DetailStoreView.Middleware

extension DetailStoreView {
  struct Middleware: OGViewStoreMiddleware {
    private let mapService: MapServicing
    private let tracker: StoreFinderTracking

    init(
      mapService: MapServicing = StoreFinderContainer.shared.mapService(),
      tracker: StoreFinderTracking = StoreFinderTracker()
    ) {
      self.mapService = mapService
      self.tracker = tracker
    }

    func callAsFunction(event: Event, for state: ViewState) async -> Event? {
      switch event {
      case .loadMapImage:
        let image = await mapService.loadMapImage(for: state.store)
        return .mapImageLoaded(image)

      case .openInMaps:
        mapService.openInMaps(for: state.store)
        return nil

      case let .callNumber(number):
        mapService.callNumber(number)
        return nil

      case ._fetchStore, ._googleMapsIsInstalled, .mapImageLoaded, .showMapAlert:  return nil

      case .didAppear:
        return ._trackDidAppear

      case ._trackDidAppear:
        tracker.trackDetailScreen()
        return nil

      case ._checkForGoogleMaps:
        return mapService.isGoogleMapsInstalled() ? ._googleMapsIsInstalled : nil

      case .openInGoogleMaps:
        mapService.openInGoogleMaps(for: state.store)
        return nil
      }
    }
  }
}

// MARK: - DetailStoreView.Connector

extension DetailStoreView {
  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    private let store: StoreFinder.Store

    init(store: StoreFinder.Store) {
      self.store = store
    }

    func configure(dispatch: @escaping (Event) async -> Void) async {
      await dispatch(._fetchStore(store))
      await dispatch(.loadMapImage)
      await dispatch(._checkForGoogleMaps)
    }
  }
}
