import MapKit
import OGL10n
import OGNavigationBar
import Swift<PERSON>
import UICatalog

// MARK: - DetailStoreView

struct DetailStoreView: View {
  @StateObject private var viewStore: Self.Store
  private let title: String
  private let buttonStyleResolver: ButtonStyleFactory

  public init(
    store: StoreFinder.Store,
    title: String,
    buttonStyleResolver: ButtonStyleFactory = StoreFinderContainer.shared.buttonStyleResolver()
  ) {
    _viewStore = StateObject(wrappedValue: Self.make(store: store))
    self.title = title
    self.buttonStyleResolver = buttonStyleResolver
  }

  var body: some View {
    OGNavigationBar(
      titleView: Text(title),
      content: {
        content
          .onAppear {
            Task {
              await viewStore.dispatch(.didAppear)
            }
          }
      }
    )
    .alert(ogL10n.ShopFinder.ShopLocationOnMap, isPresented: Binding(
      get: { viewStore.state.showMapAlert },
      set: { show in Task { await viewStore.dispatch(.showMapAlert(show)) } }
    ), actions: {
      Button(ogL10n.ShopFinder.ShowMapDialog.ActionOk) {
        Task {
          await viewStore.dispatch(.openInMaps)
        }
      }
      if viewStore.hasGoogleMapsInstalled {
        Button(ogL10n.ShopFinder.ShowMapDialog.ActionGoogleMaps) {
          Task {
            await viewStore.dispatch(.openInGoogleMaps)
          }
        }
      }
      Button(ogL10n.General.Cancel, role: .cancel) {}
    }, message: {
      Text(ogL10n.ShopFinder.ShowMapDialog.Message)
    })
  }

  private var content: some View {
    ScrollView {
      if let mapImage = viewStore.mapImage {
        Image(uiImage: mapImage)
          .resizable()
          .frame(height: UILayoutConstants.DetailStoreView.imageHeight)
          .onTapGesture {
            Task {
              await viewStore.dispatch(.openInMaps)
            }
          }
          .accessibilityHidden(true)
      }
      VStack(spacing: UILayoutConstants.Default.padding2x) {
        storeInfoSection
        actionButtons
        openingHoursSection
        descriptionSection
      }
      .padding()
    }
  }

  private var storeInfoSection: some View {
    HStack(spacing: UILayoutConstants.Default.padding2x) {
      IconView(icon: OGImages.icon24x24Location.image)
      VStack(alignment: .leading, spacing: UILayoutConstants.Default.paddingHalf) {
        Text(viewStore.store.name)
          .applyDetailStoreStyle(font: .titleM)
        if let storeTypeText = viewStore.storeTypeText {
          Text(storeTypeText)
            .font(for: .copyMRegular)
            .foregroundStyle(OGColors.textBlack60.color)
        }
        Text(viewStore.store.addressStreet)
          .applyDetailStoreStyle(font: .copyMRegular)
        Text(viewStore.store.addressPostcode + " " + viewStore.store.addressCity)
          .applyDetailStoreStyle(font: .copyMRegular)
      }
      Spacer()
    }
    .accessibilityElement(children: .combine)
  }

  private var actionButtons: some View {
    VStack {
      C2AButton(
        title: ogL10n.ShopFinder.CellDetailInfo.Button.Title,
        accessibilityIdentifier: ogL10n.ShopFinder.CellDetailInfo.Button.Title
      ) {
        Task {
          await viewStore.dispatch(.showMapAlert(true))
        }
      }
      .register(buttonStyleResolver.secondaryButtonStyle)
      if viewStore.store.contactPhone != nil {
        C2AButton(
          title: ogL10n.ShopFinder.Call.Shop,
          accessibilityIdentifier: ogL10n.ShopFinder.Call.Shop
        ) {
          Task {
            await viewStore.dispatch(.callNumber(viewStore.state.store.contactPhone ?? ""))
          }
        }
        .register(buttonStyleResolver.secondaryButtonStyle)
      }
    }
  }

  @ViewBuilder private var openingHoursSection: some View {
    if let hours = viewStore.store.openingHoursStruct {
      HStack(alignment: .top, spacing: UILayoutConstants.Default.padding2x) {
        IconView(icon: OGImages.icon24x24Hours.image)
        VStack(alignment: .leading) {
          Text(ogL10n.ShopFinder.Opening.Hours)
            .applyDetailStoreStyle(font: .titleM)
            .padding(.bottom, UILayoutConstants.DetailStoreView.bottomPadding)
            .accessibilityAddTraits(.isHeader)
          OpeningHoursView(openingHoursStruct: hours)
        }
        Spacer()
      }
    }
  }

  @ViewBuilder private var descriptionSection: some View {
    if let description = viewStore.store.description {
      HStack(alignment: .top, spacing: UILayoutConstants.Default.padding2x) {
        IconView(icon: OGImages.icon24x24InfoOnDark.image)
        VStack(alignment: .leading) {
          Text(ogL10n.ShopFinder.More.Information)
            .applyDetailStoreStyle(font: .titleM)
            .padding(.bottom, UILayoutConstants.DetailStoreView.bottomPadding)
          Text(description)
            .applyDetailStoreStyle(font: .copyMRegular)
        }
        Spacer()
      }
    }
  }
}

extension Text {
  func applyDetailStoreStyle(font: OGFonts, lineLimit: Int? = nil) -> some View {
    self
      .font(for: font)
      .multilineTextAlignment(.leading)
      .lineLimit(lineLimit)
      .foregroundColor(OGColors.textOnLight.color)
      .frame(maxWidth: .infinity, alignment: .leading)
  }
}

// MARK: - UILayoutConstants.DetailStoreView

extension UILayoutConstants {
  enum DetailStoreView {
    static let imageHeight: CGFloat = 170
    static let bottomPadding: CGFloat = 5
  }
}
