import OGCore
import OGFeatureAdapter
import OGNavigation
import <PERSON><PERSON>

// MARK: - DetailStoreDestinationProvider

public struct DetailStoreDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .detailStore

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    if let store: Store = route.getData() {
      viewWithTitle(for: route) { title in
        DetailStoreView(store: store, title: title)
      }
    }
  }

  public func title(for route: OGRoute?) -> String? {
    let store: Store? = route?.getData()
    return store?.name
  }
}

extension OGRoute {
  public static let detailStore = OGRoute(OGIdentifier.detailStore.value)
}

extension OGIdentifier {
  public static let detailStore = #identifier("detailStore")
}
