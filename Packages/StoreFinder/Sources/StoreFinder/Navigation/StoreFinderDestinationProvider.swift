import OGCore
import OGFeatureAdapter
import OGNavigation
import <PERSON><PERSON>

// MARK: - StoreFinderDestinationProvider

public struct StoreFinderDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .storeFinder

  public init() {}

  public func provide(_: OGRoute) -> some View {
    StoreFinderView()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGRoute {
  public static let storeFinder = OGRoute(OGIdentifier.storeFinder.value)
}
