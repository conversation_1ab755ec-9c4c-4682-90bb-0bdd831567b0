import MapKit
import UICatalog
import UIKit

// MARK: - MapServicing

public protocol MapServicing {
  func loadMapImage(for store: StoreFinder.Store) async -> UIImage?
  func openInMaps(for store: StoreFinder.Store)
  func callNumber(_ number: String)
  func isGoogleMapsInstalled() -> Bool
  func openInGoogleMaps(for store: StoreFinder.Store)
}

// MARK: - MapSnapshotter

public protocol MapSnapshotter {
  func start(completionHandler: @escaping (MKMapSnapshotter.Snapshot?, Error?) -> Void)
}

// MARK: - MKMapSnapshotterWrapper

final class MKMapSnapshotterWrapper: MapSnapshotter {
  private let snapshotter: MKMapSnapshotter

  init(options: MKMapSnapshotter.Options) {
    self.snapshotter = MKMapSnapshotter(options: options)
  }

  func start(completionHandler: @escaping (MKMapSnapshotter.Snapshot?, Error?) -> Void) {
    snapshotter.start(completionHandler: completionHandler)
  }
}

// MARK: - MapService

final class MapService: MapServicing {
  private let mapSnapshotterFactory: (MKMapSnapshotter.Options) -> MapSnapshotter
  private let screenBounds: () -> CGRect

  init(
    mapSnapshotterFactory: @escaping (MKMapSnapshotter.Options) -> MapSnapshotter = { MKMapSnapshotterWrapper(options: $0) },
    screenBounds: @escaping () -> CGRect = { UIScreen.main.bounds }
  ) {
    self.mapSnapshotterFactory = mapSnapshotterFactory
    self.screenBounds = screenBounds
  }

  func loadMapImage(for store: StoreFinder.Store) async -> UIImage? {
    guard let latitude = Double(store.locationLat), let longitude = Double(store.locationLon) else {
      return nil
    }
    let storeCoordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    let options = MKMapSnapshotter.Options()
    let width = screenBounds().width
    options.size = CGSize(width: width, height: UILayoutConstants.DetailStoreView.imageHeight)
    options.camera = MKMapCamera(lookingAtCenter: storeCoordinate, fromEyeCoordinate: storeCoordinate, eyeAltitude: 3_000)
    let snapshotter = mapSnapshotterFactory(options)
    return await withCheckedContinuation { continuation in
      snapshotter.start { mapSnapshot, error in
        guard let mapSnapshot, error == nil else {
          continuation.resume(returning: nil)
          return
        }
        continuation.resume(returning: self.renderMapImage(with: mapSnapshot, for: store))
      }
    }
  }

  private func renderMapImage(with mapSnapshot: MKMapSnapshotter.Snapshot, for store: StoreFinder.Store) -> UIImage? {
    let size = mapSnapshot.image.size
    let renderer = UIGraphicsImageRenderer(size: size)

    let image = renderer.image { _ in
      mapSnapshot.image.draw(at: .zero)

      let pinImage = UIImage(named: "storefinderStorepin")
      let storeCoordinate = CLLocationCoordinate2D(latitude: Double(store.locationLat)!, longitude: Double(store.locationLon)!)
      var point = mapSnapshot.point(for: storeCoordinate)
      point.x -= (pinImage?.size.width ?? 0) / 2
      point.y -= (pinImage?.size.height ?? 0) / 2
      pinImage?.draw(at: point)
    }

    return image
  }

  func openInMaps(for store: StoreFinder.Store) {
    guard let latitude = Double(store.locationLat), let longitude = Double(store.locationLon) else { return }
    let coordinate = CLLocationCoordinate2D(latitude: latitude, longitude: longitude)
    let placemark = MKPlacemark(coordinate: coordinate)
    let mapItem = MKMapItem(placemark: placemark)
    mapItem.name = store.name
    mapItem.openInMaps(launchOptions: nil)
  }

  func callNumber(_ number: String) {
    if let url = URL(string: "tel://\(number)"), UIApplication.shared.canOpenURL(url) {
      DispatchQueue.main.async {
        UIApplication.shared.open(url)
      }
    }
  }

  func isGoogleMapsInstalled() -> Bool {
    // Needs to have comgooglemaps & comgooglemaps-x-callback in info.plist under `Queried URL Schemes`
    guard let googleMapsURL = URL(string: "comgooglemaps://") else { return false }

    return UIApplication.shared.canOpenURL(googleMapsURL)
  }

  func openInGoogleMaps(for store: StoreFinder.Store) {
    guard let latitude = Double(store.locationLat), let longitude = Double(store.locationLon) else { return }

    if let url = URL(string: "comgooglemaps-x-callback://?saddr=&daddr=\(latitude),\(longitude)") {
      DispatchQueue.main.async {
        UIApplication.shared.open(url, options: [:])
      }
    }
  }
}
