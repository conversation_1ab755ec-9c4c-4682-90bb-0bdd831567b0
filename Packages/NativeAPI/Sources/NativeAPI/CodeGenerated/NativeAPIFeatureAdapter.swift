// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros


public protocol NativeAPIFeatureConfigurable {
	var isEnabled: Bool { get set }
	var graphQLApiUrl: String { get set }
	var productIdRegex: String { get set }
  var lascana: LascanaConfig? { get set }
  var witt: WittConfig? { get set }
}

public struct LascanaConfig: Codable, Equatable  {
  var restApiUrl: String
  var dynamicYield: DynamicYield
  public struct DynamicYield: Codable, Equatable {
    var trackPageViewUrl: String
    var cookiesUrl: String
  }
}
    

public struct WittConfig: Codable, Equatable {
  var locale: String
  var displayPaybackPoints: Bool
  var cookies: Cookies
  public struct Cookies: Codable, Equatable {
    var url: String
    var tokenCookieName: String
    var recoSessionIdCookieName: String
  }
}

struct NativeAPIFeatureConfig: NativeAPIFeatureConfigurable {
  var isEnabled: Bool = false
  var graphQLApiUrl: String = ""
  var productIdRegex: String = ""
  var lascana: LascanaConfig?
  var witt: WittConfig?
}

public final class NativeAPIFeatureAdapter: OGFeatureAdapter, NativeAPIFeatureAdaptable {
	override public class var featureName: OGFeature.Name { OGIdentifier.nativeAPI.value }
	
	public let configuration: CurrentValueSubject<NativeAPIFeatureConfigurable, Never>
	private var subscriptions = Set<AnyCancellable>()

	public init(configuration: NativeAPIFeatureConfigurable?) {
    self.configuration = CurrentValueSubject(configuration ?? NativeAPIFeatureConfig())
    super.init()
    	
    receiveUpdates()
  }

	private func receiveUpdates() {
		$feature.sink { [weak self] feature in
			guard let self = self else { return }
			var updatedConfiguration = self.configuration.value
			guard let feature = feature else {
				updatedConfiguration.isEnabled = false
				self.configuration.send(updatedConfiguration)
				return
			}
			updatedConfiguration.isEnabled = feature.isEnabled
			
      if let wittData = feature.custom[OGFeatureKey.CustomValues.NativeAPI.witt.value],
         let data = try? JSONEncoder().encode(wittData),
         let witt = try? JSONDecoder().decode(WittConfig.self, from: data){
        updatedConfiguration.witt = witt
      } else  if let lascanaData = feature.custom[OGFeatureKey.CustomValues.NativeAPI.lascana.value],
         let data = try? JSONEncoder().encode(lascanaData),
         let lascana = try? JSONDecoder().decode(LascanaConfig.self, from: data){
        updatedConfiguration.lascana = lascana
      }
			
			let graphQLApiUrl: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.graphQLApiUrl)) ?? self.configuration.value.graphQLApiUrl
			updatedConfiguration.graphQLApiUrl = graphQLApiUrl
			let productIdRegex: String = (feature.customValue(for: OGFeatureKey.CustomValues.NativeAPI.productIdRegex)) ?? self.configuration.value.productIdRegex
			updatedConfiguration.productIdRegex = productIdRegex
			self.configuration.send(updatedConfiguration)
			}.store(in: &subscriptions)
	}
}

public protocol NativeAPIFeatureAdaptable: OGFeatureAdaptable {
	var configuration: CurrentValueSubject<NativeAPIFeatureConfigurable, Never> { get }
}


extension OGFeatureKey.CustomValues {
	public enum NativeAPI: String, OGKeyReceivable {
			public var value: String  {
				rawValue
			}
			case graphQLApiUrl = "graphQLApiUrl"
			case productIdRegex = "productIdRegex"
      case witt = "witt"
      case lascana = "lascana"
		}
}


extension OGIdentifier {
	public static let nativeAPI = #identifier("nativeAPI")
}
