import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGCore
import OGDomainStore
import OGFeatureAdapter
import <PERSON><PERSON>outer
import OGSecret

public typealias NativeAPIStore = OGDomainStore<NativeAPIState, NativeAPIAction>
public typealias OGNativeApiTenant = Skie.com_ottogroup_ogappkit_nativeui__api.OGNativeConfig.__Sealed

extension OGDomainStoreFactory {
  static func make() -> NativeAPIStore {
    NativeAPIStore(
      reducer: NativeAPIState.Reducer.reduce,
      middlewares: NativeAPIState.Middleware(),
      connector: NativeAPIState.Connector()
    )
  }
}

// MARK: - NativeAPIState

public struct NativeAPIState: OGDomainState {
  public private(set) var isAwaitingUpdate: Bool
  public private(set) var isConfigured: Bool
  init(
    isAwaitingUpdate: Bool = false,
    isConfigured: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.isConfigured = isConfigured
  }
  
  public static let initial: Self = .init()
  
  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    isConfigured: Bool? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    self.isConfigured = isConfigured ?? self.isConfigured
  }
}

// MARK: - NativeAPIAction

public enum NativeAPIAction: OGDomainAction {
  /// private
  case _isConfigured(Bool)
  case _receiveConfig(
    graphQLApiUrl: String,
    productIdRegex: String,
    wittConfig: WittConfig?,
    lascanaConfig: LascanaConfig?,
    user: String,
    password: String,
    dynamicYieldApiKey: String?
  )
}

extension NativeAPIState {
  enum Reducer {
    static func reduce(
      _ state: inout NativeAPIState,
      with action: NativeAPIAction
    ) {
      switch action {
      case ._receiveConfig:
        state.update(isAwaitingUpdate: false)
      case let ._isConfigured(configured):
        state.update(isConfigured: configured)
      }
    }
  }
  
  struct Middleware: OGDomainMiddleware {
    let nativeAPI: OGNative
    let baseUrl: OGBaseUrlFeatureAdaptable
    let nativeAPICookiesBridge: CookiesBridge
    
    init(
      nativeAPI: OGNative = NativeAPIContainer.shared.nativeAPI(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl(),
      nativeAPICookiesBridge: CookiesBridge = NativeAPIContainer.shared.nativeAPICookiesBridge()
    ) {
      self.nativeAPI = nativeAPI
      self.baseUrl = baseUrl
      self.nativeAPICookiesBridge = nativeAPICookiesBridge
    }
    
    func callAsFunction(
      action: NativeAPIAction,
      for state: NativeAPIState
    ) async throws
    -> NativeAPIAction? {
      switch action {
      case let ._receiveConfig(
        graphQLApiUrl,
        productIdRegex,
        wittConfig,
        lascanaConfig,
        user,
        password,
        dynamicYieldApiKey
      ):
        
        let graphQLBackend = OGNativeConfigBackend(
          url: graphQLApiUrl,
          basicAuthUser: user,
          basicAuthPassword: password
        )
        
        if let wittConfig {
          
          let nativeConfig = OGNativeConfigWitt(
            graphQLBackend: graphQLBackend,
            productIdRegex: productIdRegex,
            cookiesBridge: nativeAPICookiesBridge,
            locale: wittConfig.locale,
            cookies: OGNativeConfigWitt.Cookies(
              url: wittConfig.cookies.url,
              tokenCookieName: wittConfig.cookies.tokenCookieName,
              recoSessionIdCookieName: wittConfig.cookies.recoSessionIdCookieName
            ),
            webShopBaseUrl: baseUrl.web.value?.absoluteString ?? "",
            displayPaybackPoints: wittConfig.displayPaybackPoints,
            debug: false
          )
          nativeAPI.configure(config: nativeConfig)
          
          return ._isConfigured(true)
        } else if let lascanaConfig, let dynamicYieldApiKey {
          
          let dynamicYield = OGNativeConfigLascana.DynamicYield(
            trackPageViewUrl: lascanaConfig.dynamicYield.trackPageViewUrl,
            apiKey: dynamicYieldApiKey,
            cookiesUrl: baseUrl.web.value?.absoluteString ?? ""
          )
          
          let restBackend = OGNativeConfigBackend(
            url: lascanaConfig.restApiUrl,
            basicAuthUser: user,
            basicAuthPassword: password
          )
          
          let nativeConfig = OGNativeConfigLascana(
            graphQLBackend: graphQLBackend,
            productIdRegex: productIdRegex,
            cookiesBridge: nativeAPICookiesBridge,
            restBackend: restBackend,
            dynamicYield: dynamicYield,
            debug: false
          )
          nativeAPI.configure(config: nativeConfig)
          return ._isConfigured(true)
        } else {
          return ._isConfigured(false)
        }
      case ._isConfigured:
        return nil
      }
    }
  }
  
  actor Connector: OGDomainConnector {
    private let feature: NativeAPIFeatureAdaptable
    private let secretService: OGSecretsProvidable
    
    private var cancellables = Set<AnyCancellable>()
    
    init(
      feature: NativeAPIFeatureAdaptable = NativeAPIFeatureAdapterContainer.shared.nativeAPI(),
      secretService: OGSecretsProvidable = OGSecretServiceContainer.shared.service()
    ) {
      self.feature = feature
      self.secretService = secretService
    }
    
    public func configure(
      dispatch: @escaping (NativeAPIAction) async -> Void
    ) async {
      cancellables.removeAll()
      
      let configPublisher = feature.configuration
        .receive(on: DispatchQueue.main)
      
      let graphQLApiUrlPublisher = configPublisher
        .map(\.graphQLApiUrl)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()
      
      let productIdRegexPublisher = configPublisher
        .map(\.productIdRegex)
        .compactMap {
          $0.isEmpty ? nil : $0
        }
        .removeDuplicates()
      
      let wittConfigPublisher = configPublisher
        .map(\.witt)
        .removeDuplicates()
      
      let lascanaConfigPublisher = configPublisher
        .map(\.lascana)
        .removeDuplicates()
      
      let secretServicePublisher = secretService.secretSetDidChangePublisher
        .receive(on: DispatchQueue.main)
        .compactMap {
          if let apiSecret = $0.apiSecret,
             let apiUser = $0.apiUser {
            return (apiSecret: apiSecret, apiUser: apiUser, dynamicYieldApiKey: $0.dynamicYieldApiKey)
          } else {
            return (apiSecret: "", apiUser: "", dynamicYieldApiKey: "")
          }
        }
      
      let featuresAPIPublishers = Publishers.CombineLatest3(
        graphQLApiUrlPublisher,
        wittConfigPublisher,
        lascanaConfigPublisher
      )
      
      Publishers
        .CombineLatest3(featuresAPIPublishers, productIdRegexPublisher, secretServicePublisher)
        .map { featuresAPI, productIdRegex, secrets in
          (featuresAPI.0, featuresAPI.1, featuresAPI.2,  productIdRegex, secrets)
        }
        .receive(on: DispatchQueue.main)
        .sink { graphQLApiUrl, wittConfig, lascanaConfig, productIdRegex, secret in
          
          Task { [graphQLApiUrl, wittConfig, lascanaConfig, productIdRegex, secret] in
            await dispatch(
              ._receiveConfig(
                graphQLApiUrl: graphQLApiUrl,
                productIdRegex: productIdRegex,
                wittConfig: wittConfig,
                lascanaConfig: lascanaConfig,
                user: secret.apiUser,
                password: secret.apiSecret,
                dynamicYieldApiKey: secret.dynamicYieldApiKey
              )
            )
          }
        }
        .store(in: &cancellables)
    }
  }
}

// MARK: - OGIdentifier.SecretIdentifiers

extension OGIdentifier {
  enum SecretIdentifiers {
    enum NativeAPI {
      static let apiSecret = #identifier("graphQLApiSecret")
      static let apiUser = #identifier("graphQLApiUser")
    }
    
    enum DynamicYield {
      static let apiKey = #identifier("dynamicYieldApiKey")
    }
  }
}

extension Set<OGSecret> {
  var apiSecret: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.NativeAPI.apiSecret })?.secret
  }
  
  var apiUser: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.NativeAPI.apiUser })?.secret
  }
  
  var dynamicYieldApiKey: String? {
    first(where: { $0.identifier == OGIdentifier.SecretIdentifiers.DynamicYield.apiKey })?.secret
  }
}
