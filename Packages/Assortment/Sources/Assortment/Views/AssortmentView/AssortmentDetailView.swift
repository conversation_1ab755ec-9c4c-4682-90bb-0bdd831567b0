import AppCore
import OGAsyncImage
import OGL10n
import OGNavigationBar
import Search
import SwiftUI
import UICatalog

// MARK: - AssortmentDetailView

struct AssortmentDetailView: View {
  @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
  @StateObject private var viewStore: Self.Store
  @SwiftUI.State private var scrollOffset = CGFloat.zero
  @EnvironmentObject private var deviceState: DeviceState

  private let title: String

  public init(
    title: String,
    navigationEntry: AssortmentState.NavigationEntry
  ) {
    self.title = title
    self._viewStore = StateObject(
      wrappedValue: AssortmentDetailView.make(navigationEntry: navigationEntry)
    )
  }

  var body: some View {
    OGNavigationBar(
      titleView: titleView,
      hasSearchIcon: OGObservedBoolean(true)
    ) {
      content
    }
  }

  var titleView: some View {
    Text(title)
  }

  var content: some View {
    VStack(spacing: UILayoutConstants.AssortmentView.overlapSpacing) {
      if let imageUrl = viewStore.imageUrl, !deviceState.isLandscapeOrientation {
        HStack {
          OGAsyncImage(
            url: imageUrl,
            placeholderImage: OGImages.assortmentFirstLevelBannerFallback.image
          )
          .frame(height: getHeightForHeaderImage(scrollOffset))
          .clipped()
          .offset(x: 0, y: getOffsetForHeaderImage(scrollOffset))
        }
        .frame(height: UILayoutConstants.AssortmentView.teaserHeight)
        .accessibilityHidden(true)
      }
      ObservableScrollView(scrollOffset: $scrollOffset, showsIndicators: false) { _ in
        LazyVStack(spacing: .zero) {
          ForEach(viewStore.childEntries.indices, id: \.self) { index in
            if let entry = viewStore.childEntries[safe: index] {
              AssortmentItemView(
                index: index,
                count: viewStore.childEntries.count,
                navigationEntry: entry
              )
              .background(OGColors.backgroundBackground0.color)
            }
          }
          if viewStore.childEntries.count < 4 {
            // Add solid color footer when there are not enough entries to overlap the teaser header
            OGColors.backgroundBackground0.color
              .frame(height: UILayoutConstants.Default.bannerHeight)
          }
        }
        .accessibilityElement(children: .contain)
        .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: "\(viewStore.childEntries.count)"))
      }
    }
  }

  private func getHeightForHeaderImage(_ offset: CGFloat) -> CGFloat {
    let imageHeight = UILayoutConstants.AssortmentView.teaserHeight
    let overlap = UILayoutConstants.AssortmentView.overlapSpacing
    let adjustedOffset = offset + overlap

    if adjustedOffset > 0 {
      return imageHeight + adjustedOffset
    }
    return imageHeight
  }

  private func getOffsetForHeaderImage(_ offset: CGFloat) -> CGFloat {
    let overlap = UILayoutConstants.AssortmentView.overlapSpacing
    let adjustedOffset = offset + overlap

    if adjustedOffset > 0 {
      return adjustedOffset / 2
    }
    return 0
  }
}

// MARK: - AssortmentView_Previews

struct AssortmentView_Previews: PreviewProvider {
  static var previews: some View {
    let child1 = AssortmentEntry(
      children: [],
      displayType: nil,
      label: "Node Child",
      l10n: nil,
      type: .node,
      teaser: nil,
      icon: nil,
      url: nil
    )
    let child2 = AssortmentEntry(
      children: [],
      displayType: nil,
      label: "Link Child",
      l10n: nil,
      type: .link,
      teaser: nil,
      icon: nil,
      url: nil
    )
    let children: [AssortmentNavigationEntry] = [child1, child2]
      .compactMap { .assortmentEntry($0) }
    let dummyEntry = AssortmentEntry(
      children: children,
      displayType: nil,
      label: "Dummy",
      l10n: nil,
      type: .link,
      teaser: Teaser(
        url: URL(string: "google.com"),
        style: nil
      ),
      icon: nil,
      url: nil
    )
    AssortmentDetailView(
      title: "Detail",
      navigationEntry: AssortmentState.NavigationEntry(
        assortmentNavigationEntry: .assortmentEntry(dummyEntry),
        nodeType: .leaf
      )
    )
  }
}

// MARK: - UILayoutConstants.AssortmentView

extension UILayoutConstants {
  enum AssortmentView {
    static let teaserHeight = UIDevice.current.userInterfaceIdiom == .pad ? 500.0 : 250.0
    static let overlapSpacing = Default.bannerHeight - teaserHeight
  }
}
