import OGNavigationBar
import Search
import SwiftUI
import UICatalog

// MARK: - AssortmentRootView

struct AssortmentRootView: View {
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var shouldScrollUp = false
  @SwiftUI.State private var scrollOffset = CGFloat.zero
  @SwiftUI.State private var contentOffset = CGPoint.zero

  private let title: String

  public init(
    title: String,
    viewStore: Self.Store = AssortmentRootView.make()
  ) {
    self.title = title
    self._viewStore = StateObject(wrappedValue: viewStore)
  }

  var body: some View {
    if viewStore.isEnabled {
      OGNavigationBar(
        contentOffset: $contentOffset,
        titleView: titleView,
        hasSearchBar: OGObservedBoolean(true),
        hasSearchIcon: OGObservedBoolean(true),
        content: {
          content()
        }
      )
      .onChange(
        of: scrollOffset
      ) { _ in
        contentOffset = CGPoint(
          x: 0,
          y: -scrollOffset
        )
      }
      .onAppear {
        Task {
          await viewStore.dispatch(.didAppear)
        }
      }
    }
  }

  private var titleView: some View {
    Text(title)
  }

  @ViewBuilder
  private func content() -> some View {
    if viewStore.hasTabEntries {
      AssortmentHorizontalNavigationView(
        scrollOffset: $scrollOffset,
        shouldScrollUp: $shouldScrollUp
      )
    } else {
      AssortmentVerticalNavigationView(
        scrollOffset: $scrollOffset,
        shouldScrollUp: $shouldScrollUp
      )
    }
  }
}

// MARK: - AssortmentRootView_Previews

struct AssortmentRootView_Previews: PreviewProvider {
  static var previews: some View {
    AssortmentRootView(title: "Assortment", viewStore: AssortmentRootView.make())
  }
}
