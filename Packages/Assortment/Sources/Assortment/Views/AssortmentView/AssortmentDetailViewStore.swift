import AsyncAlgorithms
import Combine
import Foundation
import OGDomainStore
import OGIdentifier
import OGL10n
import OGRouter
import OGTracker
import OGViewStore

// MARK: - AssortmentDetailView.Store

extension AssortmentDetailView {
  typealias Store = OGViewStore<State, Event>
}

// MARK: - Factory

extension AssortmentDetailView {
  static func make(
    navigationEntry: AssortmentState.NavigationEntry
  ) -> Store {
    AssortmentDetailView.Store(
      initialState: State(navigationEntry: navigationEntry),
      reducer: Reducer.reduce,
      middleware: Middleware()
    )
  }
}

// MARK: - AssortmentDetailView.Event

extension AssortmentDetailView {
  enum Event: OGViewEvent {
    case didSelectEntry
  }
}

// MARK: - AssortmentDetailView.State

extension AssortmentDetailView {
  struct State: OGViewState {
    fileprivate let navigationEntry: AssortmentState.NavigationEntry

    var childEntries: [AssortmentState.NavigationEntry] {
      navigationEntry.childEntries
    }

    var imageUrl: URL? {
      navigationEntry.imageUrl
    }

    var url: URL? {
      navigationEntry.url
    }

    private(set) var isEnabled: Bool = true

    init(
      navigationEntry: AssortmentState.NavigationEntry
    ) {
      self.navigationEntry = navigationEntry
    }

    static let initial: State = .init(navigationEntry: .empty)
  }
}

// MARK: - AssortmentDetailView.Reducer

extension AssortmentDetailView {
  enum Reducer {
    static func reduce(state: inout State, with event: Event) {}
  }
}

// MARK: - AssortmentDetailView.Middleware

extension AssortmentDetailView {
  struct Middleware: OGViewStoreMiddleware {
    private let navigationEntrySelectionHandler: NavigationEntrySelectionHandling

    init(
      navigationEntrySelectionHandler: NavigationEntrySelectionHandling = NavigationEntrySelectionHandler()
    ) {
      self.navigationEntrySelectionHandler = navigationEntrySelectionHandler
    }

    func callAsFunction(event: Event, for state: State) async -> Event? {
      switch event {
      case .didSelectEntry:
        navigationEntrySelectionHandler.didSelect(state.navigationEntry)
        return nil
      }
    }
  }
}
