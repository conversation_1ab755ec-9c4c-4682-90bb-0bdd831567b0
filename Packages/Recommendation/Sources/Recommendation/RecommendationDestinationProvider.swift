import Foundation
import OGCore
import OGDIService
import OGRouter
import SwiftUI

public struct RecommendationDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.recommendation
  }

  public func provide(_ route: OGRoute) -> some View {
    RecommendationView()
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.medium])
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
