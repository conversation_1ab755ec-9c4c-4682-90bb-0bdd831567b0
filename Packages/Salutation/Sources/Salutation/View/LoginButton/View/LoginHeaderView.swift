import SwiftUI
import UICatalog

// MARK: - LoginHeaderView

public struct LoginHeaderView: View {
  @StateObject private var viewStore: Self.Store

  public init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    Text(viewStore.title)
      .environment(\.openURL, OpenURLAction { _ in
        Task {
          await viewStore.dispatch(.didTapLogin)
        }
        return .discarded
      })
      .lineLimit(2)
      .multilineTextAlignment(.center)
      .accessibilityAddTraits(.isButton)
      .accessibilityIdentifier("navigationLoginButtonTitle")
  }
}

extension Font {
  static var link: Font {
    let style = FontResolverContainer.shared.fontResolver().font(.titleM)
    return Font(
      UIFont(name: style.name, size: CGFloat(style.size)) ?? UIFont.boldSystemFont(ofSize: 16)
    )
  }
}
