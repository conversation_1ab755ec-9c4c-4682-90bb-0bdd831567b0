import Combine
import OGAppLifecycle
import OGCoreTestsUtils
import OGNavigation
import OGNavigationCoreTestsUtils
import OGRouterTestsUtils
import OGWebViewTestsUtils
import XCTest
@testable import AppCore

final class ScreenAnnouncerConnectorTests: XCTestCase {
  private enum TestConstants {
    static let screenTitle = "Title"
    static let tabName = "TabTest"
  }

  func test_GIVEN_titlePublisher_WHEN_valueEmitted_THEN_dispatchesSetCurrentScreenName() async {
    let expectation = XCTestExpectation(description: "Dispatched ._setCurrentScreenName action")
    let mockTitlePublisher = OGTitlePublisherMock()
    let connector = ScreenAnnouncerState.Connector(
      titlePublisher: mockTitlePublisher,
      tabTapPublisher: OGTabTapMock(),
      routePublisher: OGRoutePublisherMock()
    )
    var dispatched: [ScreenAnnouncerAction] = []

    await connector.configure { action in
      dispatched.append(action)
      if case let ._setCurrentScreenName(title) = action, title == TestConstants.screenTitle {
        expectation.fulfill()
      }
    }

    await mockTitlePublisher.provider.send(TestConstants.screenTitle)

    await fulfillment(of: [expectation], timeout: 1.0)

    XCTAssertTrue(
      dispatched.contains(where: {
        if case let ._setCurrentScreenName(title) = $0 { return title == TestConstants.screenTitle } else { return false }
      })
    )
  }

  func test_GIVEN_tabTapPublisher_WHEN_tabTapped_THEN_dispatchesSetCurrentTab() async {
    let expectation = XCTestExpectation(description: "Dispatched ._setCurrentTab action")
    let mockTabTapPublisher = OGTabTapMock()
    let connector = ScreenAnnouncerState.Connector(
      titlePublisher: OGTitlePublisherMock(),
      tabTapPublisher: mockTabTapPublisher,
      routePublisher: OGRoutePublisherMock()
    )
    var dispatched: [ScreenAnnouncerAction] = []

    await connector.configure { action in
      dispatched.append(action)
      if case let ._setCurrentTab(tabName) = action, tabName == TestConstants.tabName {
        expectation.fulfill()
      }
    }

    mockTabTapPublisher.didTapPublisher.send(OGTab(name: TestConstants.tabName, rawValue: TestConstants.tabName))

    await fulfillment(of: [expectation], timeout: 1.0)

    XCTAssertTrue(
      dispatched.contains(where: {
        if case let ._setCurrentTab(tabName) = $0 { return tabName == TestConstants.tabName } else { return false }
      })
    )
  }

  func test_GIVEN_appLifecycleStore_WHEN_foregroundStateEmitted_THEN_dispatchesDidEnterForeground() async {
    let expectation = XCTestExpectation(description: "Dispatched ._didEnterForeground action")
    let mockAppLifecycleStore = OGAppLifecycleStore.make()
    let connector = ScreenAnnouncerState.Connector(
      appLifecycleStore: mockAppLifecycleStore,
      titlePublisher: OGTitlePublisherMock(),
      tabTapPublisher: OGTabTapMock(),
      routePublisher: OGRoutePublisherMock()
    )
    var dispatched: [ScreenAnnouncerAction] = []

    await connector.configure { action in
      dispatched.append(action)
      if case ._didEnterForeground = action {
        expectation.fulfill()
      }
    }

    mockAppLifecycleStore.dispatchDetached(OGAppLifecycleAction._willEnterForeground)

    await fulfillment(of: [expectation], timeout: 1.0)

    XCTAssertTrue(
      dispatched.contains(where: {
        if case ._didEnterForeground = $0 { return true } else { return false }
      })
    )
  }

  func test_GIVEN_webReloadAfterErrorPublisher_WHEN_reloadEmitted_THEN_dispatchesDidWebReload() async {
    let expectation = XCTestExpectation(description: "Dispatched ._didWebReload action")
    let mockWebReloadPublisher = OGWebReloadAfterErrorPublisherMock()
    let connector = ScreenAnnouncerState.Connector(
      titlePublisher: OGTitlePublisherMock(),
      tabTapPublisher: OGTabTapMock(),
      routePublisher: OGRoutePublisherMock(),
      webReloadAfterErrorPublisher: mockWebReloadPublisher
    )
    var dispatched: [ScreenAnnouncerAction] = []

    await connector.configure { action in
      dispatched.append(action)
      if case ._didWebReloadAfterError = action {
        expectation.fulfill()
      }
    }

    mockWebReloadPublisher.send(true)

    await fulfillment(of: [expectation], timeout: 1.0)

    XCTAssertTrue(
      dispatched.contains(where: {
        if case ._didWebReloadAfterError = $0 { return true } else { return false }
      })
    )
  }
}
