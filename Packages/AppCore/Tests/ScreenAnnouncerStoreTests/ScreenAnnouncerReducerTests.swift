import OGNavigationCore
import OGNavigationCoreTestsUtils
import OGNavigationTestsUtils
import XCTest
@testable import AppCore

final class ScreenAnnouncerReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_setCurrentScreen_THEN_currentIsSet() {
    var state = ScreenAnnouncerState.initial
    let screen = ScreenView.mainStub
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentScreen(screen))
    XCTAssertEqual(state.current, screen)
    XCTAssertTrue(state.history.isEmpty)
  }

  func test_GIVEN_initialState_WHEN_setCurrentTab_AND_noExistingTab_THEN_currentAndHistoryAreUpdated() {
    var state = ScreenAnnouncerState.initial
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentTab(ScreenView.homeStub.tabName))
    XCTAssertEqual(state.current?.tabName, ScreenView.homeStub.tabName)
    XCTAssertNotNil(state.current)
    XCTAssertTrue(state.history.contains { $0.tabName == ScreenView.homeStub.tabName })
  }

  func test_GIVEN_existingTabInHistory_WHEN_setCurrentTab_THEN_currentIsSetToExistingTab() {
    let existing = ScreenView.mainStub
    var state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [existing], current: nil)
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentTab(ScreenView.homeStub.tabName))
    XCTAssertEqual(state.current, existing)
    XCTAssertEqual(state.history.count, 1)
  }

  func test_GIVEN_existingTabInHistory_WHEN_setCurrentTabWithDifferentTab_THEN_onlyNewTabIsAdded() {
    let existing = ScreenView.mainStub
    var state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [existing], current: nil)
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentTab(ScreenView.assortmentStub.tabName))
    XCTAssertEqual(state.current?.tabName, ScreenView.assortmentStub.tabName)
    XCTAssertEqual(state.history.count, 2)
    XCTAssertTrue(state.history.contains(existing))
    XCTAssertTrue(state.history.contains { $0.tabName == ScreenView.assortmentStub.tabName })
  }

  func test_GIVEN_currentTab_WHEN_setCurrentScreenName_THEN_screenNameIsUpdatedInHistoryAndCurrent() {
    let existing = ScreenView(screenName: nil, tabName: ScreenView.homeStub.tabName)
    var state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [existing], current: existing)
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentScreenName(ScreenView.mainScreenStub.screenName!))
    XCTAssertEqual(state.current?.screenName, ScreenView.mainScreenStub.screenName)
    let expectedTabName = ScreenView.homeStub.tabName
    let expectedScreenName = ScreenView.mainScreenStub.screenName
    let viewMatches: (ScreenView) -> Bool = { view in
      let tabMatches = view.tabName == expectedTabName
      let screenNameMatches = view.screenName == expectedScreenName
      return tabMatches && screenNameMatches
    }
    let matchFound = state.history.contains(where: viewMatches)
    XCTAssertTrue(matchFound)
  }

  func test_GIVEN_currentTab_WHEN_setCurrentScreenName_TitleContainsTab_THEN_screenNameBecomesNil() {
    let existing = ScreenView(screenName: nil, tabName: ScreenView.assortmentStub.tabName)
    var state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [existing], current: existing)
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentScreenName("Assortment Shop"))
    XCTAssertNil(state.current?.screenName)
    let tabName = ScreenView.assortmentStub.tabName
    let containsNilScreenName = state.history.contains { view in
      let tabMatches = view.tabName == tabName
      let screenNameIsNil = view.screenName == nil
      return tabMatches && screenNameIsNil
    }
    XCTAssertTrue(containsNilScreenName)
  }

  func test_GIVEN_initial_WHEN_didEnterForeground_THEN_noop() {
    var state = ScreenAnnouncerState.initial
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._didEnterForeground)
    XCTAssertEqual(state, .initial)
  }

  func test_GIVEN_multipleTabs_WHEN_setCurrentScreenName_updatesCorrectTabOnly() {
    let tabA = ScreenView(screenName: nil, tabName: ScreenView.homeStub.tabName)
    let tabB = ScreenView.assortmentTitleBStub
    var state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [tabA, tabB], current: tabA)
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._setCurrentScreenName(ScreenView.updatedTitleStub.screenName!))
    XCTAssertEqual(state.current?.screenName, ScreenView.updatedTitleStub.screenName)
    let homeTabUpdated = state.history.contains { view in
      let isHomeTab = view.tabName == ScreenView.homeStub.tabName
      let isUpdatedScreen = view.screenName == ScreenView.updatedTitleStub.screenName
      return isHomeTab && isUpdatedScreen
    }
    let assortmentTabUnchanged = state.history.contains { view in
      let isAssortmentTab = view.tabName == ScreenView.assortmentStub.tabName
      let isOriginalScreen = view.screenName == ScreenView.assortmentTitleBStub.screenName
      return isAssortmentTab && isOriginalScreen
    }
    XCTAssertTrue(homeTabUpdated)
    XCTAssertTrue(assortmentTabUnchanged)
  }

  func test_GIVEN_initial_WHEN_didWebReloadAfterError_THEN_noop() {
    var state = ScreenAnnouncerState.initial
    ScreenAnnouncerState.Reducer.reduce(&state, with: ._didWebReloadAfterError)
    XCTAssertEqual(state, .initial)
  }
}
