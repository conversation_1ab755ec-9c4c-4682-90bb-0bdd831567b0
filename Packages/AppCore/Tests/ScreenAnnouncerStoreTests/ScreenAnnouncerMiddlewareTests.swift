import AppCoreTestsUtils
import OGL10n
import OGNavigationCoreTestsUtils
import XCTest
@testable import AppCore

final class ScreenAnnouncerMiddlewareTests: XCTestCase {
  func test_GIVEN_historyWithMatchingTab_WHEN_setCurrentTab_THEN_dispatchesSetCurrentScreen() async throws {
    let history = [ScreenView.inboxStub]
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: Set(history), current: history[0])
    let sut = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: AccessibilityAnnouncerMock())
    let action = try await sut.callAsFunction(action: ._setCurrentTab(ScreenView.inboxStub.tabName), for: state)
    XCTAssertEqual(action, ._setCurrentScreen(history[0]))
  }

  func test_GIVEN_historyWithMatchingScreenName_WHEN_setCurrentScreenName_THEN_dispatchesSetCurrentScreen() async throws {
    let history = [ScreenView.inboxStub]
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: Set(history), current: history[0])
    let sut = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: AccessibilityAnnouncerMock())
    let action = try await sut.callAsFunction(action: ._setCurrentScreenName(ScreenView.inboxStub.screenName!), for: state)
    XCTAssertEqual(action, ._setCurrentScreen(history[0]))
  }

  func test_GIVEN_screenWithLabel_WHEN_setCurrentScreen_THEN_announcementSent() async throws {
    let mockAnnouncer = AccessibilityAnnouncerMock()
    let screen = ScreenView(screenName: "Screen", tabName: "Tab")
    let sut = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: mockAnnouncer)
    _ = try await sut.callAsFunction(action: ._setCurrentScreen(screen), for: .initial)
    XCTAssertEqual(mockAnnouncer.mock.announceScreenAppearanceCalls.callsCount, 1)
    XCTAssertEqual(mockAnnouncer.mock.announceScreenAppearanceCalls.latestCall?.0, screen.announcementLabel)
  }

  func test_GIVEN_screenWithoutLabel_WHEN_setCurrentScreen_THEN_noAnnouncement() async throws {
    let mockAnnouncer = AccessibilityAnnouncerMock()
    let screen = ScreenView(tabName: "Tab")
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: mockAnnouncer)
    _ = try await middleware.callAsFunction(action: ._setCurrentScreen(screen), for: .initial)
    XCTAssertEqual(mockAnnouncer.mock.announceCalls.callsCount, 0)
  }

  func test_GIVEN_currentScreenWithLabel_WHEN_didEnterForeground_THEN_announcementSent() async throws {
    let screen = ScreenView(screenName: "Screen", tabName: "Tab")
    let mockAnnouncer = AccessibilityAnnouncerMock()
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: mockAnnouncer)
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [screen], current: screen)
    _ = try await middleware.callAsFunction(action: ._didEnterForeground, for: state)
    XCTAssertEqual(mockAnnouncer.mock.announceScreenAppearanceCalls.callsCount, 1)
    XCTAssertEqual(mockAnnouncer.mock.announceScreenAppearanceCalls.latestCall?.0, screen.announcementLabel)
  }

  func test_GIVEN_noCurrentScreen_WHEN_didEnterForeground_THEN_noAnnouncement() async throws {
    let mockAnnouncer = AccessibilityAnnouncerMock()
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: mockAnnouncer)
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: [], current: nil)
    _ = try await middleware.callAsFunction(action: ._didEnterForeground, for: state)
    XCTAssertEqual(mockAnnouncer.mock.announceCalls.callsCount, 0)
  }

  func test_GIVEN_noMatchingTab_WHEN_setCurrentTab_THEN_noActionDispatched() async throws {
    let history = [ScreenView.inboxStub]
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: Set(history), current: history[0])
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: AccessibilityAnnouncerMock())
    let action = try await middleware.callAsFunction(action: ._setCurrentTab("TabX"), for: state)
    XCTAssertNil(action)
  }

  func test_GIVEN_noMatchingScreenName_WHEN_setCurrentScreenName_THEN_noActionDispatched() async throws {
    let history = [ScreenView.inboxStub]
    let state = ScreenAnnouncerState(isAwaitingUpdate: false, history: Set(history), current: history[0])
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: AccessibilityAnnouncerMock())
    let action = try await middleware.callAsFunction(action: ._setCurrentScreenName("X"), for: state)
    XCTAssertNil(action)
  }

  func test_WHEN_didWebReloadAfterError_THEN_announcementSent_AND_noActionDispatched() async throws {
    let mockAnnouncer = AccessibilityAnnouncerMock()
    let middleware = ScreenAnnouncerState.Middleware(accessibilityAnnouncer: mockAnnouncer)
    let action = try await middleware.callAsFunction(action: ._didWebReloadAfterError, for: .initial)
    XCTAssertEqual(mockAnnouncer.mock.announceScreenAppearanceCalls.callsCount, 1)
    XCTAssertEqual(
      mockAnnouncer.mock.announceScreenAppearanceCalls.latestCall?.0,
      ogL10n.Error.Generic.Reload.Accessibility
    )
    XCTAssertNil(action)
  }
}
