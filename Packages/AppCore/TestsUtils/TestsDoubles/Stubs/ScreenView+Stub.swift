import AppCore

extension ScreenView {
  public static let homeStub = ScreenView(tabName: "Home")
  public static let inboxStub = ScreenView(screenName: "Inbox", tabName: "Home")
  public static let assortmentStub = ScreenView(tabName: "Assortment")
  public static let assortmentDetailsStub = ScreenView(screenName: "Details", tabName: "Assortment")

  public static let mainStub = ScreenView(screenName: "Main", tabName: ScreenView.homeStub.tabName)
  public static let mainScreenStub = ScreenView(screenName: "MainScreen", tabName: ScreenView.homeStub.tabName)
  public static let assortmentTitleBStub = ScreenView(screenName: "TitleB", tabName: ScreenView.assortmentStub.tabName)
  public static let updatedTitleStub = ScreenView(screenName: "UpdatedTitle", tabName: ScreenView.homeStub.tabName)
}
