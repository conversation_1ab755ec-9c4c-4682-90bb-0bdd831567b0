import Foundation
import OGDIService

// MARK: - AppConfigurable

public protocol AppConfigurable {
  #if DEBUG || BETA
  // BetaConfigurable properties
  #endif
}

// MARK: - AppConfiguration

public struct AppConfiguration: AppConfigurable {}

// MARK: - AppContainer

public final class AppContainer: OGDISharedContainer {
  public private(set) static var shared: AppContainer = .init()
  public private(set) var manager: OGDIContainerManager = .init()

  public var appStore: OGDIService<any AppStoreProtocol> {
    self {
      AppStore()
    }
    .singleton
  }

  public var appConfiguration: OGDIService<AppConfigurable> {
    self {
      AppConfiguration()
    }.cached
  }

  public var notificationObservable: OGDIService<NotificationObservable> {
    self {
      NotificationCenter.default
    }.cached
  }

  public var notificationCenter: OGDIService<NotificationCenter> {
    self {
      NotificationCenter.default
    }.cached
  }

  public var accessibilityAnnouncer: OGDIService<any AccessibilityAnnouncing> {
    self {
      AccessibilityAnnouncer()
    }
  }

  public var screenAnnouncerStore: OGDIService<ScreenAnnouncerStore> {
    self {
      ScreenAnnouncerStore.make()
    }.cached
  }
}
