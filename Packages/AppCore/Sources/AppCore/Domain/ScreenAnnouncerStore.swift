import Combine
import Foundation
import OGAppLifecycle
import OGCore
import OGDIService
import OGDomainStore
import OGL10n
import OGNavigationCore
import OGRouter
import OGWebBridge
import OGWebView

// MARK: - Typealias

public typealias ScreenAnnouncerStore = OGDomainStore<ScreenAnnouncerState, ScreenAnnouncerAction>

// MARK: - Store Factory

extension OGDomainStore where State == ScreenAnnouncerState, Action == ScreenAnnouncerAction {
  static func make() -> ScreenAnnouncerStore {
    ScreenAnnouncerStore(
      reducer: ScreenAnnouncerState.Reducer.reduce,
      middlewares: ScreenAnnouncerState.Middleware(),
      connector: ScreenAnnouncerState.Connector()
    )
  }
}

// MARK: - ScreenView

public struct ScreenView: Equatable, Hashable, Sendable {
  public let screenName: String?
  public let tabName: String

  public var announcementLabel: String? {
    guard let screenName else { return nil }
    return "\(tabName) - \(screenName)"
  }

  public init(screenName: String? = nil, tabName: String) {
    self.screenName = screenName
    self.tabName = tabName
  }
}

// MARK: - ScreenAnnouncerState

public struct ScreenAnnouncerState: OGDomainState {
  private(set) var history: Set<ScreenView>
  private(set) var current: ScreenView?
  public private(set) var isAwaitingUpdate: Bool

  public static let initial = ScreenAnnouncerState()

  init(
    isAwaitingUpdate: Bool = false,
    history: Set<ScreenView> = [],
    current: ScreenView? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.history = history
    self.current = current
  }

  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    history: Set<ScreenView>? = nil,
    current: ScreenView? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    self.history = history ?? self.history
    self.current = current ?? self.current
  }
}

// MARK: - ScreenAnnouncerAction

public enum ScreenAnnouncerAction: OGDomainAction, Equatable {
  case _setCurrentScreen(ScreenView)
  case _setCurrentTab(String)
  case _setCurrentScreenName(String)
  case _didEnterForeground
  case _didWebReloadAfterError
}

// MARK: - ScreenAnnouncerState.Reducer

extension ScreenAnnouncerState {
  enum Reducer: OGDomainActionReducible {
    static func reduce(
      _ state: inout ScreenAnnouncerState,
      with action: ScreenAnnouncerAction
    ) {
      switch action {
      case let ._setCurrentScreen(screen):
        state.update(current: screen)

      case let ._setCurrentTab(tabName):
        let (updatedHistory, newScreen) = replaceTabIfNeeded(
          in: state.history,
          with: tabName
        )
        state.update(history: updatedHistory, current: newScreen)

      case let ._setCurrentScreenName(screenName):
        guard let currentTab = state.current?.tabName else { break }

        let (updatedHistory, newScreen) = replaceScreenName(
          in: state.history,
          for: currentTab,
          screenName: screenName
        )
        state.update(history: updatedHistory, current: newScreen)

      case ._didEnterForeground:
        break

      case ._didWebReloadAfterError:
        break
      }
    }

    private static func replaceTabIfNeeded(
      in history: Set<ScreenView>,
      with tabName: String
    ) -> (Set<ScreenView>, ScreenView) {
      if let existing = history.first(where: { $0.tabName == tabName }) {
        return (history, existing)
      } else {
        let new = ScreenView(tabName: tabName)
        var updated = history
        updated.insert(new)
        return (updated, new)
      }
    }

    private static func replaceScreenName(
      in history: Set<ScreenView>,
      for tab: String,
      screenName: String
    ) -> (Set<ScreenView>, ScreenView) {
      let titleWords = screenName
        .components(separatedBy: .whitespacesAndNewlines)
        .filter { !$0.isEmpty }

      let isTabTitle = titleWords.contains(tab)

      let updated = ScreenView(
        screenName: isTabTitle ? nil : screenName,
        tabName: tab
      )

      let filtered = history.filter { $0.tabName != tab }
      return (filtered.union([updated]), updated)
    }
  }
}

// MARK: - ScreenAnnouncerState.Middleware

extension ScreenAnnouncerState {
  struct Middleware: OGDomainMiddleware {
    private let accessibilityAnnouncer: AccessibilityAnnouncing

    init(
      accessibilityAnnouncer: AccessibilityAnnouncing = AppContainer.shared.accessibilityAnnouncer()
    ) {
      self.accessibilityAnnouncer = accessibilityAnnouncer
    }

    func callAsFunction(
      action: ScreenAnnouncerAction,
      for state: ScreenAnnouncerState
    ) async throws -> ScreenAnnouncerAction? {
      switch action {
      case let ._setCurrentTab(tabName):
        return screenFromTab(tabName, in: state)

      case let ._setCurrentScreenName(screenName):
        return screenFromName(screenName, in: state)

      case let ._setCurrentScreen(screen):
        guard let label = screen.announcementLabel else { return nil }
        await accessibilityAnnouncer.announceScreenAppearance(
          message: label,
          config: .screenAnnouncer
        )
        return nil

      case ._didEnterForeground:
        guard
          let screen = state.current,
          let label = screen.announcementLabel
        else { return nil }
        await accessibilityAnnouncer.announceScreenAppearance(
          message: label,
          config: .screenAnnouncer
        )
        return nil

      case ._didWebReloadAfterError:
        await accessibilityAnnouncer.announceScreenAppearance(
          message: ogL10n.Error.Generic.Reload.Accessibility,
          config: .screenAnnouncer
        )
        return nil
      }
    }

    private func screenFromTab(_ tabName: String, in state: ScreenAnnouncerState) -> ScreenAnnouncerAction? {
      let matches = state.history.filter { $0.tabName == tabName }

      guard !matches.isEmpty else { return nil }
      return matches
        .first(where: { $0.screenName != nil })
        .map(ScreenAnnouncerAction._setCurrentScreen)
        ?? matches
        .first
        .map(ScreenAnnouncerAction._setCurrentScreen)
    }

    private func screenFromName(_ screenName: String, in state: ScreenAnnouncerState) -> ScreenAnnouncerAction? {
      if let match = state.history.first(where: { $0.screenName == screenName }) {
        return ._setCurrentScreen(match)
      }

      if let tabMatch = state.history.first(where: { $0.tabName == screenName }) {
        return ._setCurrentScreen(tabMatch)
      }

      return nil
    }
  }
}

// MARK: - ScreenAnnouncerState.Connector

extension ScreenAnnouncerState {
  actor Connector: OGDomainConnector {
    private let appLifecycleStore: OGAppLifecycleStore
    private let titlePublisher: OGTitlePublishing
    private let tabTapPublisher: OGTabTapping
    private let routePublisher: any OGRoutePublishing
    private let webReloadAfterErrorPublisher: OGWebReloadAfterErrorPublishing

    private var cancellables = Set<AnyCancellable>()

    init(
      appLifecycleStore: OGAppLifecycleStore = OGAppLifecycleContainer.shared.appLifecycleStore(),
      titlePublisher: OGTitlePublishing = OGCoreContainer.shared.titlePublisher(),
      tabTapPublisher: OGTabTapping = OGNavigationCoreContainer.shared.tabTap(),
      routePublisher: any OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      webReloadAfterErrorPublisher: OGWebReloadAfterErrorPublishing = OGWebViewContainer.shared.webReloadAfterErrorPublisher()
    ) {
      self.appLifecycleStore = appLifecycleStore
      self.titlePublisher = titlePublisher
      self.tabTapPublisher = tabTapPublisher
      self.routePublisher = routePublisher
      self.webReloadAfterErrorPublisher = webReloadAfterErrorPublisher
    }

    func configure(dispatch: @escaping (ScreenAnnouncerAction) async -> Void) async {
      await setupTitlePublisher(dispatch)
      setupTabTapPublisher(dispatch)
      await setupAppLifecyclePublisher(dispatch)
      setupWebReloadAfterErrorPublisher(dispatch)
    }

    private func setupTitlePublisher(_ dispatch: @escaping (ScreenAnnouncerAction) async -> Void) async {
      await Publishers.Merge(
        routePublisher.screenTitlePublisher,
        titlePublisher.provider
          .compactMap { $0 }
      )
      .removeDuplicates()
      .sink { title in
        Task {
          await dispatch(._setCurrentScreenName(title))
        }
      }
      .store(in: &cancellables)
    }

    private func setupTabTapPublisher(_ dispatch: @escaping (ScreenAnnouncerAction) async -> Void) {
      tabTapPublisher.didTapPublisher
        .map {
          ogL10n.resolve(key: $0.name)
        }
        .sink { tabName in
          Task { await dispatch(._setCurrentTab(tabName)) }
        }
        .store(in: &cancellables)
    }

    private func setupAppLifecyclePublisher(_ dispatch: @escaping (ScreenAnnouncerAction) async -> Void) async {
      await appLifecycleStore
        .watch(keyPath: \.appLifeCycleState)
        .compactMap { $0 == .foreground ? () : nil }
        .sink {
          Task { await dispatch(._didEnterForeground) }
        }
        .store(in: &cancellables)
    }

    private func setupWebReloadAfterErrorPublisher(_ dispatch: @escaping (ScreenAnnouncerAction) async -> Void) {
      webReloadAfterErrorPublisher
        .publisher
        .sink { didReload in
          guard didReload else { return }
          Task { await dispatch(._didWebReloadAfterError) }
        }
        .store(in: &cancellables)
    }
  }
}

// MARK: - Accessibility Announcement Config

extension AnnouncementConfig {
  static var screenAnnouncer = AnnouncementConfig(
    screenChangedDelay: 1_500_000_000,
    announcementDelay: 1_000_000_000
  )
}
