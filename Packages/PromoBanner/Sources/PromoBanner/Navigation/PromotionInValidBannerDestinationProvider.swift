import OGCore
import OGDIService
import OGL10n
import OGRouter
import <PERSON><PERSON>
import UICatalog

// MARK: - PromotionInvalidBannerDestinationProvider

// swiftlint:disable type_name
public struct PromotionInvalidBannerDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier = OGIdentifier.promotionInvalidBanner
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public init() {}

  public func provide(_: OGRoute) -> some View {
    BannerView(
      image: OGImages.icon24x24Danger.image,
      text: ogL10n.ShakeADeal.InAppMessage.InvalidCode,
      backgroundColor: OGColors.backgroundBackground0.color,
      dividerColor: OGColors.accentDanger.color
    )
    .accessibilityElement()
    .onAppear {
      DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(5)) {
        router.dismiss()
      }
    }
  }

  public func presentationType() -> OGPresentationType {
    .banner
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  public static let promotionInvalidBanner = #identifier("promotionInvalidBanner")
}

extension OGRoute {
  public static let promotionInvalidBanner = OGRoute(OGIdentifier.promotionInvalidBanner.value)
}
