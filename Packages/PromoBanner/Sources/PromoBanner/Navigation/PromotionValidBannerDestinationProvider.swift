import OGCore
import OGDIService
import OGL10n
import OGRouter
import Swift<PERSON>
import UICatalog

// MARK: - PromotionValidBannerDestinationProvider

public struct PromotionValidBannerDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier = OGIdentifier.promotionValidBanner
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    BannerView(
      image: OGImages.icon24x24Success.image,
      text: ogL10n.ShakeADeal.InAppMessage.ValidCode,
      backgroundColor: OGColors.backgroundBackground0.color,
      dividerColor: OGColors.accentSuccess.color
    )
    .accessibilityElement()
    .onAppear {
      DispatchQueue.main.asyncAfter(deadline: .now() + .seconds(5)) {
        router.dismiss()
      }
    }
  }

  public func presentationType() -> OGPresentationType {
    .banner
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  public static let promotionValidBanner = #identifier("promotionValidBanner")
}

extension OGRoute {
  public static let promotionValidBanner = OGRoute(OGIdentifier.promotionValidBanner.value)
}
