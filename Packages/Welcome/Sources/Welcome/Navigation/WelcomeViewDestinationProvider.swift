import OGMacros
import OGNavigation
import SwiftUI

// MARK: - WelcomeViewDestinationProvider

public struct WelcomeViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.welcome
  }

  public func provide(_ route: OGRoute) -> some View {
    WelcomeScreen()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  static let welcome = #identifier("onboarding")
}

extension OGRoute {
  public static let welcome = OGRoute(OGIdentifier.welcome.value)
}

extension OGNavigationItem {
  public static let welcome = OGNavigationItem(route: .welcome)
}
