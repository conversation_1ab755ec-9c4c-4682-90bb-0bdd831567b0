import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - ModalTenantChooserDestinationProvider

public struct ModalTenantChooserDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.modalTenantChooser
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      TenantChooserView(presentationStyle: .modal, title: title)
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet()
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.CountrySelection.Title
  }
}

// MARK: - PushTenantChooserDestinationProvider

public struct PushTenantChooserDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.pushTenantChooser
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      TenantChooserView(presentationStyle: .push, title: title)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.CountrySelection.Title
  }
}

extension OGIdentifier {
  static let modalTenantChooser = #identifier("modalTenantChooser")
  static let pushTenantChooser = #identifier("app://pushTenantChooser")
}

extension OGRoute {
  public static let modalTenantChooser = OGRoute(
    OGIdentifier.modalTenantChooser.value,
    presentation: .sheet()
  )

  public static let pushTenantChooser = OGRoute(
    OGIdentifier.pushTenantChooser.value,
    presentation: .push
  )
}
