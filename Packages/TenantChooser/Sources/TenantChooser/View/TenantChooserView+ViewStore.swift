import AppCore
import Combine
import Foundation
import OGAppKitSDK
import OGDomainStore
import OGL10n
import OGNavigation
import OGTenantCore
import OGTracker
import OGViewStore
import TenantChooserCore

// MARK: - TenantChooserView.Store

extension TenantChooserView {
  typealias Store = OGViewStore<ViewState, Event>
}

extension TenantChooserView {
  @MainActor
  static func make(presentationStyle: PresentationStyle) -> Store {
    Store(
      initialState: ViewState(presentationStyle: presentationStyle),
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector()
    )
  }
}

// MARK: - TenantChooserView.Event

extension TenantChooserView {
  enum Event: OGViewEvent {
    case close
    case didAppear
    /// Private
    case _update(tenantsInfo: TenantsInfo)
    case _trackDidAppear
  }
}

// MARK: - TenantChooserView.PresentationStyle

extension TenantChooserView {
  enum PresentationStyle {
    case modal
    case push
  }
}

// MARK: - TenantChooserView.ViewState

extension TenantChooserView {
  struct ViewState: OGViewState {
    let tenantsInfo: TenantsInfo
    let presentationStyle: PresentationStyle

    init(
      tenantsInfo: TenantsInfo,
      presentationStyle: PresentationStyle
    ) {
      self.tenantsInfo = tenantsInfo
      self.presentationStyle = presentationStyle
    }

    init(presentationStyle: PresentationStyle) {
      self.init(
        tenantsInfo: TenantsInfo.initial,
        presentationStyle: .modal
      )
    }

    var closeButtonTitle: String {
      ogL10n.General.Close
    }

    var accessibilityHintOverlay: String {
      ogL10n.General.Overlay
    }

    func copy(with tenantsInfo: TenantsInfo) -> ViewState {
      ViewState(
        tenantsInfo: tenantsInfo,
        presentationStyle: presentationStyle
      )
    }

    static var initial = TenantChooserView.ViewState(
      tenantsInfo: TenantsInfo.initial,
      presentationStyle: .modal
    )
  }
}

extension TenantChooserView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case ._trackDidAppear, .close, .didAppear:
        break

      case let ._update(model):
        state = state.copy(with: model)
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let tracker: OGTrackerProtocol

    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.router = router
      self.tracker = tracker
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch (event, state.presentationStyle) {
      case (.close, .modal):
        router.dismiss()
        return nil

      case (.close, .push):
        router.pop()
        return nil

      case (._update, _):
        return nil

      case (.didAppear, _):
        return ._trackDidAppear

      case (._trackDidAppear, _):
        tracker.multiplatformTrack(event: ViewEvent.ScreenCountryScreen())
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let appStore: any AppStoreProtocol
    private var cancellables = Set<AnyCancellable>()

    init(
      appStore: any AppStoreProtocol = AppContainer.shared.appStore()
    ) {
      self.appStore = appStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      let tenantsInfo =
        await appStore
          .watch(keyPath: \.tenantState)
          .filter { $0.isAwaitingUpdate == false }
          .map(\.tenantsInfo)
          .removeDuplicates()

      tenantsInfo
        .sink { tenantsInfo in
          Task {
            await dispatch(
              ._update(
                tenantsInfo: tenantsInfo
              )
            )
          }
        }
        .store(in: &cancellables)
    }
  }
}
