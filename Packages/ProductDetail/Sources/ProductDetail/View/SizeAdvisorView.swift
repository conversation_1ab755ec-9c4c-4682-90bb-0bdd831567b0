import SwiftUI
import UICatalog



// MARK: - Main Component
struct SizeAdvisorView: View {
    let sizeTableData: SizeTableData
    @Environment(\.dismiss) private var dismiss

    private let horizontalScreenPadding: CGFloat = 16
    private let imageAspectRatio: CGFloat = 16/9

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 0) {
                    // Tables
                    ForEach(Array(sizeTableData.tables.enumerated()), id: \.offset) { _, tableData in
                        VStack(alignment: .leading, spacing: 0) {
                            // Table headline
                            if let headline = tableData.headline {
                                Text(headline)
                                    .font(for: .headlineXL)
                                    .foregroundColor(OGColors.textPrimary.color)
                                    .padding(.horizontal, horizontalScreenPadding)
                                    .padding(.vertical, 24)
                            }
                            
                            // Table detail items
                            if let tableDetailsItems = tableData.tableDetailsItems {
                                ForEach(Array(tableDetailsItems.enumerated()), id: \.offset) { _, item in
                                    TableDetailItemView(item: item)
                                }
                            }
                            
                            // Size table
                            SizeTableView(table: tableData.table)
                        }
                    }
                    
                    Spacer()
                        .frame(height: 32)
                    
                    // Details section
                    VStack(alignment: .leading, spacing: 16) {
                        if let detailsHeadline = sizeTableData.detailsHeadline {
                            Text(detailsHeadline)
                                .font(for: .headlineXL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(.horizontal, horizontalScreenPadding)
                        }

                        if let detailsDescription = sizeTableData.detailsDescription {
                            Text(detailsDescription)
                                .font(for: .copyL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(.horizontal, horizontalScreenPadding)
                        }
                        
                        if !sizeTableData.detailsItems.isEmpty {
                            // Details image
                            if let imageUrl = sizeTableData.detailsImageUrl {
                                AsyncImage(url: URL(string: imageUrl)) { image in
                                    image
                                        .resizable()
                                        .aspectRatio(imageAspectRatio, contentMode: .fit)
                                } placeholder: {
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.3))
                                        .aspectRatio(imageAspectRatio, contentMode: .fit)
                                }
                                .padding(.horizontal, horizontalScreenPadding)
                            }
                            
                            // Details items
                            VStack(alignment: .leading, spacing: 16) {
                                ForEach(Array(sizeTableData.detailsItems.enumerated()), id: \.offset) { index, item in
                                    VStack(alignment: .leading, spacing: 8) {
                                        SizeDetailsHeader(
                                            index: "\(index + 1)",
                                            text: item.title
                                        )
                                        
                                        Text(item.description)
                                            .font(for: .copyL)
                                            .foregroundColor(OGColors.textPrimary.color)
                                    }
                                }
                            }
                            .padding(.horizontal, horizontalScreenPadding)
                        }
                    }
                    
                    Spacer()
                        .frame(height: 32)
                    
                    // Tip card
                    if let tipText = sizeTableData.tipText {
                        VStack {
                            Text("💡 \(tipText)")
                                .font(for: .copyL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(16)
                        }
                        .background(OGColors.backgroundBackground10.color)
                        .cornerRadius(12)
                        .padding(.horizontal, horizontalScreenPadding)
                    }
                    
                    Spacer()
                        .frame(height: 32)
                }
            }
            .background(OGColors.backgroundBackground0.color)
            .navigationTitle("Größentabelle")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button {
                        dismiss()
                    } label: {
                        Image(systemName: "xmark")
                            .foregroundColor(OGColors.textPrimary.color)
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Views
struct TableDetailItemView: View {
    let item: SizeTableDetailsItem
    private let horizontalScreenPadding: CGFloat = 16
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(buildAttributedString())
                .padding(.horizontal, horizontalScreenPadding)
        }
        .padding(.bottom, 16)
    }
    
    private func buildAttributedString() -> AttributedString {
        var attributedString = AttributedString()

        var titlePart = AttributedString("\(item.title): ")
        titlePart.font = .headline
        titlePart.foregroundColor = OGColors.textPrimary.color

        var descriptionPart = AttributedString(item.description)
        descriptionPart.font = .body
        descriptionPart.foregroundColor = OGColors.textPrimary.color

        attributedString.append(titlePart)
        attributedString.append(descriptionPart)

        return attributedString
    }
}

struct SizeTableView: View {
    let table: [String: [String]]
    private let horizontalScreenPadding: CGFloat = 16
    private let columnWidth: CGFloat = 90
    
    var body: some View {
        if table.isEmpty { return AnyView(EmptyView()) }
        
        let keys = Array(table.keys)
        let maxValueCount = table.values.map { $0.count }.max() ?? 0
        
        return AnyView(
            VStack(spacing: 0) {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyVStack(spacing: 0) {
                        // Header row
                        HStack(spacing: 0) {
                            Spacer()
                                .frame(width: horizontalScreenPadding)
                            
                            ForEach(keys, id: \.self) { key in
                                VStack {
                                    Text(key)
                                        .font(for: .titleM)
                                        .foregroundColor(OGColors.textPrimary.color)
                                        .multilineTextAlignment(.center)
                                        .padding(.vertical, 12)
                                        .padding(.horizontal, 8)
                                }
                                .frame(width: columnWidth)
                                .background(OGColors.backgroundBackground10.color)
                                .overlay(
                                    Rectangle()
                                        .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
                                )
                            }
                            
                            Spacer()
                                .frame(width: horizontalScreenPadding)
                        }
                        
                        // Data rows
                        ForEach(0..<maxValueCount, id: \.self) { rowIndex in
                            HStack(spacing: 0) {
                                Spacer()
                                    .frame(width: horizontalScreenPadding)
                                
                                ForEach(keys, id: \.self) { key in
                                    let values = table[key] ?? []
                                    let value = rowIndex < values.count ? values[rowIndex] : "-"

                                    VStack {
                                        Text(value)
                                            .font(for: .copyMRegular)
                                            .foregroundColor(OGColors.textPrimary.color)
                                            .multilineTextAlignment(.center)
                                            .padding(.vertical, 12)
                                            .padding(.horizontal, 8)
                                    }
                                    .frame(width: columnWidth)
                                    .background(OGColors.backgroundBackground0.color)
                                    .overlay(
                                        Rectangle()
                                            .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
                                    )
                                }
                                
                                Spacer()
                                    .frame(width: horizontalScreenPadding)
                            }
                        }
                    }
                }
            }
            .padding(.vertical, 16)
        )
    }
}

struct SizeDetailsHeader: View {
    let index: String
    let text: String
    
    var body: some View {
        HStack(alignment: .center) {
            ZStack {
                Circle()
                    .fill(OGColors.accentSuccess.color)
                    .frame(width: 32, height: 32)

                Text(index)
                    .font(for: .caption)
                    .foregroundColor(OGColors.textOnPrimary.color)
            }
            .accessibilityHidden(true)

            Text(text)
                .font(for: .headlineLEmphasized)
                .foregroundColor(OGColors.textPrimary.color)
                .padding(.leading, 8)
            
            Spacer()
        }
    }
}
