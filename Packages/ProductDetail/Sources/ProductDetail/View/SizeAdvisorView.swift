import SwiftUI



// MARK: - Main Component
struct SizeAdvisorView: View {
    let sizeTableData: SizeTableData
    
    private let horizontalScreenPadding: CGFloat = 16
    private let imageAspectRatio: CGFloat = 16/9
    
    var body: some View {
        
            ScrollView {
                VStack(alignment: .leading, spacing: 0) {
                    // Tables
                    ForEach(Array(sizeTableData.tables.enumerated()), id: \.offset) { _, tableData in
                        VStack(alignment: .leading, spacing: 0) {
                            // Table headline
                            if let headline = tableData.headline {
                                Text(headline)
                                    .font(.title2)
                                    .fontWeight(.bold)
                                    .foregroundColor(.primary)
                                    .padding(.horizontal, horizontalScreenPadding)
                                    .padding(.vertical, 24)
                            }
                            
                            // Table detail items
                            if let tableDetailsItems = tableData.tableDetailsItems {
                                ForEach(Array(tableDetailsItems.enumerated()), id: \.offset) { _, item in
                                    TableDetailItemView(item: item)
                                }
                            }
                            
                            // Size table
                            SizeTableView(table: tableData.table)
                        }
                    }
                    
                    Spacer()
                        .frame(height: 32)
                    
                    // Details section
                    VStack(alignment: .leading, spacing: 16) {
                        if let detailsHeadline = sizeTableData.detailsHeadline {
                            Text(detailsHeadline)
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                                .padding(.horizontal, horizontalScreenPadding)
                        }
                        
                        if let detailsDescription = sizeTableData.detailsDescription {
                            Text(detailsDescription)
                                .font(.body)
                                .foregroundColor(.primary)
                                .padding(.horizontal, horizontalScreenPadding)
                        }
                        
                        if !sizeTableData.detailsItems.isEmpty {
                            // Details image
                            if let imageUrl = sizeTableData.detailsImageUrl {
                                AsyncImage(url: URL(string: imageUrl)) { image in
                                    image
                                        .resizable()
                                        .aspectRatio(imageAspectRatio, contentMode: .fit)
                                } placeholder: {
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.3))
                                        .aspectRatio(imageAspectRatio, contentMode: .fit)
                                }
                                .padding(.horizontal, horizontalScreenPadding)
                            }
                            
                            // Details items
                            VStack(alignment: .leading, spacing: 16) {
                                ForEach(Array(sizeTableData.detailsItems.enumerated()), id: \.offset) { index, item in
                                    VStack(alignment: .leading, spacing: 8) {
                                        SizeDetailsHeader(
                                            index: "\(index + 1)",
                                            text: item.title
                                        )
                                        
                                        Text(item.description)
                                            .font(.body)
                                            .foregroundColor(.primary)
                                    }
                                }
                            }
                            .padding(.horizontal, horizontalScreenPadding)
                        }
                    }
                    
                    Spacer()
                        .frame(height: 32)
                    
                    // Tip card
                    if let tipText = sizeTableData.tipText {
                        VStack {
                            Text("💡 \(tipText)")
                                .font(.body)
                                .foregroundColor(.primary)
                                .padding(8)
                        }
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                        .shadow(radius: 2)
                        .padding(.horizontal, horizontalScreenPadding)
                    }
                    
                    Spacer()
                        .frame(height: 32)
                }
            }
            .background(Color(.systemBackground))
          
        
    }
}

// MARK: - Supporting Views
struct TableDetailItemView: View {
    let item: SizeTableDetailsItem
    private let horizontalScreenPadding: CGFloat = 16
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(buildAttributedString())
                .padding(.horizontal, horizontalScreenPadding)
        }
        .padding(.bottom, 16)
    }
    
    private func buildAttributedString() -> AttributedString {
        var attributedString = AttributedString()
        
        var titlePart = AttributedString("\(item.title): ")
        titlePart.font = .headline
        titlePart.foregroundColor = .primary
        
        var descriptionPart = AttributedString(item.description)
        descriptionPart.font = .body
        descriptionPart.foregroundColor = .primary
        
        attributedString.append(titlePart)
        attributedString.append(descriptionPart)
        
        return attributedString
    }
}

struct SizeTableView: View {
    let table: [String: [String]]
    private let horizontalScreenPadding: CGFloat = 16
    private let columnWidth: CGFloat = 80
    
    var body: some View {
        if table.isEmpty { return AnyView(EmptyView()) }
        
        let keys = Array(table.keys)
        let maxValueCount = table.values.map { $0.count }.max() ?? 0
        
        return AnyView(
            VStack(spacing: 0) {
                ScrollView(.horizontal, showsIndicators: false) {
                    VStack(spacing: 0) {
                        // Header row
                        HStack(spacing: 0) {
                            Spacer()
                                .frame(width: horizontalScreenPadding)
                            
                            ForEach(keys, id: \.self) { key in
                                VStack {
                                    Text(key)
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                        .multilineTextAlignment(.center)
                                        .padding(8)
                                }
                                .frame(width: columnWidth)
                                .background(Color(.systemGray5))
                                .border(Color(.systemGray4), width: 1)
                            }
                            
                            Spacer()
                                .frame(width: horizontalScreenPadding)
                        }
                        
                        // Data rows
                        ForEach(0..<maxValueCount, id: \.self) { rowIndex in
                            HStack(spacing: 0) {
                                Spacer()
                                    .frame(width: horizontalScreenPadding)
                                
                                ForEach(keys, id: \.self) { key in
                                    let values = table[key] ?? []
                                    let value = rowIndex < values.count ? values[rowIndex] : "-"
                                    
                                    VStack {
                                        Text(value)
                                            .font(.body)
                                            .foregroundColor(.primary)
                                            .multilineTextAlignment(.center)
                                            .padding(8)
                                    }
                                    .frame(width: columnWidth)
                                    .background(Color(.systemBackground))
                                    .border(Color(.systemGray4), width: 1)
                                }
                                
                                Spacer()
                                    .frame(width: horizontalScreenPadding)
                            }
                        }
                    }
                }
            }
            .padding(.vertical, 24)
        )
    }
}

struct SizeDetailsHeader: View {
    let index: String
    let text: String
    
    var body: some View {
        HStack(alignment: .center) {
            ZStack {
                Circle()
                    .fill(Color.green)
                    .frame(width: 32, height: 32)
                
                Text(index)
                    .font(.caption)
                    .fontWeight(.bold)
                    .foregroundColor(.white)
            }
            .accessibilityHidden(true)
            
            Text(text)
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.leading, 8)
            
            Spacer()
        }
    }
}
