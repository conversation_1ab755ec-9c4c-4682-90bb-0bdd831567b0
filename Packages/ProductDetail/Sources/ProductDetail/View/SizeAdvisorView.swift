import SwiftUI
import UICatalog



// MARK: - Main Component
struct SizeAdvisorView: View {
    let sizeTableData: SizeTableData

    var body: some View {
        ScrollView {
          VStack(alignment: .leading, spacing: .zero) {
                    // Tables
                    ForEach(Array(sizeTableData.tables.enumerated()), id: \.offset) { _, tableData in
                      VStack(alignment: .leading, spacing: .zero) {
                            // Table headline
                            if let headline = tableData.headline {
                                Text(headline)
                                    .font(for: .headlineXL)
                                    .foregroundColor(OGColors.textPrimary.color)
                                    .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                                    .padding(.vertical, UILayoutConstants.SizeAdvisorView.headlinePadding)
                            }
                            
                            // Table detail items
                            if let tableDetailsItems = tableData.tableDetailsItems {
                                ForEach(Array(tableDetailsItems.enumerated()), id: \.offset) { _, item in
                                    TableDetailItemView(item: item)
                                }
                            }
                            
                            // Size table
                            SizeTableView(table: tableData.table)
                        }
                    }
                    
                    Spacer()
                        .frame(height: UILayoutConstants.SizeAdvisorView.sectionSpacing)
                    
                    // Details section
                    VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
                        if let detailsHeadline = sizeTableData.detailsHeadline {
                            Text(detailsHeadline)
                                .font(for: .headlineXL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                        }

                        if let detailsDescription = sizeTableData.detailsDescription {
                            Text(detailsDescription)
                                .font(for: .copyL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                        }
                        
                        if !sizeTableData.detailsItems.isEmpty {
                            // Details image
                            if let imageUrl = sizeTableData.detailsImageUrl {
                                AsyncImage(url: URL(string: imageUrl)) { image in
                                    image
                                        .resizable()
                                        .aspectRatio(UILayoutConstants.SizeAdvisorView.imageAspectRatio, contentMode: .fit)
                                } placeholder: {
                                    Rectangle()
                                        .fill(Color.gray.opacity(0.3))
                                        .aspectRatio(UILayoutConstants.SizeAdvisorView.imageAspectRatio, contentMode: .fit)
                                }
                                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                            }
                            
                            // Details items
                            VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
                                ForEach(Array(sizeTableData.detailsItems.enumerated()), id: \.offset) { index, item in
                                    VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.itemSpacing) {
                                        SizeDetailsHeader(
                                            index: "\(index + 1)",
                                            text: item.title
                                        )
                                        
                                        Text(item.description)
                                            .font(for: .copyL)
                                            .foregroundColor(OGColors.textPrimary.color)
                                    }
                                }
                            }
                            .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                        }
                    }
                    
                    Spacer()
                        .frame(height: UILayoutConstants.SizeAdvisorView.sectionSpacing)
                    
                    // Tip card
                    if let tipText = sizeTableData.tipText {
                        VStack {
                            Text("💡 \(tipText)")
                                .font(for: .copyL)
                                .foregroundColor(OGColors.textPrimary.color)
                                .padding(UILayoutConstants.SizeAdvisorView.tipCardPadding)
                        }
                        .background(OGColors.backgroundBackground10.color)
                        .cornerRadius(UILayoutConstants.SizeAdvisorView.tipCardCornerRadius)
                        .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                    }
                    
                    Spacer()
                        .frame(height: UILayoutConstants.SizeAdvisorView.sectionSpacing)
                }
        }
        .background(OGColors.backgroundBackground0.color)
    }
}

// MARK: - Supporting Views
struct TableDetailItemView: View {
    let item: SizeTableDetailsItem

    var body: some View {
        VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
            Text(buildAttributedString())
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
        .padding(.bottom, UILayoutConstants.SizeAdvisorView.detailsSpacing)
    }
    
    private func buildAttributedString() -> AttributedString {
        var attributedString = AttributedString()

        var titlePart = AttributedString("\(item.title): ")
        titlePart.font = .headline
        titlePart.foregroundColor = OGColors.textOnLight.color

        var descriptionPart = AttributedString(item.description)
        descriptionPart.font = .body
        descriptionPart.foregroundColor = OGColors.textOnLight.color

        attributedString.append(titlePart)
        attributedString.append(descriptionPart)

        return attributedString
    }
}

struct SizeTableView: View {
    let table: [String: [String]]
    
    var body: some View {
        if table.isEmpty { return AnyView(EmptyView()) }
        
        let keys = Array(table.keys)
        let maxValueCount = table.values.map { $0.count }.max() ?? 0
        
        return AnyView(
            VStack(spacing: 0) {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyVStack(spacing: 0) {
                        // Header row
                        HStack(spacing: 0) {
                            Spacer()
                                .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                            
                            ForEach(keys, id: \.self) { key in
                                VStack {
                                    Text(key)
                                        .font(for: .titleM)
                                        .foregroundColor(OGColors.textOnLight.color)
                                        .multilineTextAlignment(.center)
                                        .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableCellVerticalPadding)
                                        .padding(.horizontal, UILayoutConstants.SizeAdvisorView.tableCellHorizontalPadding)
                                }
                                .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)
                                .background(OGColors.backgroundBackground10.color)
                                .overlay(
                                    Rectangle()
                                        .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
                                )
                            }
                            
                            Spacer()
                                .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                        }
                        
                        // Data rows
                        ForEach(0..<maxValueCount, id: \.self) { rowIndex in
                            HStack(spacing: 0) {
                                Spacer()
                                    .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                                
                                ForEach(keys, id: \.self) { key in
                                    let values = table[key] ?? []
                                    let value = rowIndex < values.count ? values[rowIndex] : "-"

                                    VStack {
                                        Text(value)
                                            .font(for: .copyMRegular)
                                            .foregroundColor(OGColors.textOnLight.color)
                                            .multilineTextAlignment(.center)
                                            .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableCellVerticalPadding)
                                            .padding(.horizontal, UILayoutConstants.SizeAdvisorView.tableCellHorizontalPadding)
                                    }
                                    .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)
                                    .background(OGColors.backgroundBackground0.color)
                                    .overlay(
                                        Rectangle()
                                            .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
                                    )
                                }
                                
                                Spacer()
                                    .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                            }
                        }
                    }
                }
            }
            .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableVerticalPadding)
        )
    }
}

struct SizeDetailsHeader: View {
    let index: String
    let text: String
    
    var body: some View {
        HStack(alignment: .center) {
            ZStack {
                Circle()
                    .fill(OGColors.accentSuccess.color)
                    .frame(width: UILayoutConstants.SizeAdvisorView.circleSize, height: UILayoutConstants.SizeAdvisorView.circleSize)

                Text(index)
                    .font(for: .caption)
                    .foregroundColor(OGColors.textOnPrimary.color)
            }
            .accessibilityHidden(true)

            Text(text)
                .font(for: .headlineLEmphasized)
                .foregroundColor(OGColors.textOnLight.color)
                .padding(.leading, UILayoutConstants.SizeAdvisorView.itemSpacing)
            
            Spacer()
        }
    }
}

// MARK: - UILayoutConstants.SizeAdvisorView

extension UILayoutConstants {
    enum SizeAdvisorView {
        static let horizontalScreenPadding: CGFloat = 16
        static let headlinePadding: CGFloat = 24
        static let sectionSpacing: CGFloat = 32
        static let detailsSpacing: CGFloat = 16
        static let itemSpacing: CGFloat = 8
        static let tipCardPadding: CGFloat = 16
        static let tipCardCornerRadius: CGFloat = 12
        static let columnWidth: CGFloat = 90
        static let tableCellVerticalPadding: CGFloat = 12
        static let tableCellHorizontalPadding: CGFloat = 8
        static let tableVerticalPadding: CGFloat = 16
        static let circleSize: CGFloat = 32
        static let imageAspectRatio: CGFloat = 16/9
    }
}
