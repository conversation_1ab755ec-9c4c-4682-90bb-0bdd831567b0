import SwiftUI
import UICatalog



// MARK: - Main Component
struct SizeAdvisorView: View {
    let sizeTableData: SizeTableData

    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: .zero) {
                tablesSection
                sectionSpacer
                detailsSection
                sectionSpacer
                tipCardSection
                sectionSpacer
            }
        }
        .background(OGColors.backgroundBackground0.color)
    }

    // MARK: - View Components

    @ViewBuilder
    private var tablesSection: some View {
        ForEach(Array(sizeTableData.tables.enumerated()), id: \.offset) { _, tableData in
            VStack(alignment: .leading, spacing: .zero) {
                tableHeadline(tableData.headline)
                tableDetailItems(tableData.tableDetailsItems)
                SizeTableView(table: tableData.table)
            }
        }
    }

    @ViewBuilder
    private var detailsSection: some View {
        VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
            detailsHeadline
            detailsDescription
            detailsContent
        }
    }

    @ViewBuilder
    private var tipCardSection: some View {
        if let tipText = sizeTableData.tipText {
            VStack {
                Text("💡 \(tipText)")
                    .font(for: .copyL)
                    .foregroundColor(OGColors.textOnLight.color)
                    .padding(UILayoutConstants.SizeAdvisorView.tipCardPadding)
            }
            .background(OGColors.backgroundBackground10.color)
            .cornerRadius(UILayoutConstants.SizeAdvisorView.tipCardCornerRadius)
            .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
    }

    private var sectionSpacer: some View {
        Spacer()
            .frame(height: UILayoutConstants.SizeAdvisorView.sectionSpacing)
    }

    // MARK: - Helper Methods

    @ViewBuilder
    private func tableHeadline(_ headline: String?) -> some View {
        if let headline = headline {
            Text(headline)
                .font(for: .headlineXL)
                .foregroundColor(OGColors.textOnLight.color)
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
                .padding(.vertical, UILayoutConstants.SizeAdvisorView.headlinePadding)
        }
    }

    @ViewBuilder
    private func tableDetailItems(_ items: [SizeTableDetailsItem]?) -> some View {
        if let items = items {
            ForEach(Array(items.enumerated()), id: \.offset) { _, item in
                TableDetailItemView(item: item)
            }
        }
    }

    @ViewBuilder
    private var detailsHeadline: some View {
        if let detailsHeadline = sizeTableData.detailsHeadline {
            Text(detailsHeadline)
                .font(for: .headlineXL)
                .foregroundColor(OGColors.textOnLight.color)
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
    }

    @ViewBuilder
    private var detailsDescription: some View {
        if let detailsDescription = sizeTableData.detailsDescription {
            Text(detailsDescription)
                .font(for: .copyL)
                .foregroundColor(OGColors.textOnLight.color)
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
    }

    @ViewBuilder
    private var detailsContent: some View {
        if !sizeTableData.detailsItems.isEmpty {
            detailsImage
            detailsItemsList
        }
    }

    @ViewBuilder
    private var detailsImage: some View {
        if let imageUrl = sizeTableData.detailsImageUrl {
            AsyncImage(url: URL(string: imageUrl)) { image in
                image
                    .resizable()
                    .aspectRatio(UILayoutConstants.SizeAdvisorView.imageAspectRatio, contentMode: .fit)
            } placeholder: {
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .aspectRatio(UILayoutConstants.SizeAdvisorView.imageAspectRatio, contentMode: .fit)
            }
            .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
    }

    private var detailsItemsList: some View {
        VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
            ForEach(Array(sizeTableData.detailsItems.enumerated()), id: \.offset) { index, item in
                VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.itemSpacing) {
                    SizeDetailsHeader(
                        index: "\(index + 1)",
                        text: item.title
                    )

                    Text(item.description)
                        .font(for: .copyL)
                        .foregroundColor(OGColors.textOnLight.color)
                }
            }
        }
        .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
    }
}

// MARK: - Supporting Views
struct TableDetailItemView: View {
    let item: SizeTableDetailsItem

    var body: some View {
        VStack(alignment: .leading, spacing: UILayoutConstants.SizeAdvisorView.detailsSpacing) {
            Text(buildAttributedString())
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
        }
        .padding(.bottom, UILayoutConstants.SizeAdvisorView.detailsSpacing)
    }
    
    private func buildAttributedString() -> AttributedString {
        var attributedString = AttributedString()

        var titlePart = AttributedString("\(item.title): ")
        titlePart.font = .headline
        titlePart.foregroundColor = OGColors.textOnLight.color

        var descriptionPart = AttributedString(item.description)
        descriptionPart.font = .body
        descriptionPart.foregroundColor = OGColors.textOnLight.color

        attributedString.append(titlePart)
        attributedString.append(descriptionPart)

        return attributedString
    }
}

struct SizeTableView: View {
    let table: [String: [String]]
    
    var body: some View {
        if table.isEmpty {
            EmptyView()
        } else {
            VStack(spacing: 0) {
                ScrollView(.horizontal, showsIndicators: false) {
                    LazyVStack(spacing: 0) {
                        headerRow
                        dataRows
                    }
                }
            }
            .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableVerticalPadding)
        }
    }

    // MARK: - Computed Properties

    private var keys: [String] {
        Array(table.keys)
    }

    private var maxValueCount: Int {
        table.values.map { $0.count }.max() ?? 0
    }

    // MARK: - View Components

    private var headerRow: some View {
        HStack(alignment: .top, spacing: 0) {
            leadingSpacer

            ForEach(keys, id: \.self) { key in
                headerCell(for: key)
            }

            trailingSpacer
        }
        .fixedSize(horizontal: false, vertical: true)
    }

    private var dataRows: some View {
        ForEach(0..<maxValueCount, id: \.self) { rowIndex in
            dataRow(at: rowIndex)
        }
    }

    // MARK: - Helper Methods

    @ViewBuilder
    private func headerCell(for key: String) -> some View {
        ZStack {
            Rectangle()
                .fill(OGColors.backgroundBackground10.color)
                .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)

            Text(key)
                .font(for: .titleM)
                .foregroundColor(OGColors.textOnLight.color)
                .multilineTextAlignment(.center)
                .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableCellVerticalPadding)
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.tableCellHorizontalPadding)
                .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)
        }
        .overlay(
            Rectangle()
                .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
        )
    }

    @ViewBuilder
    private func dataRow(at rowIndex: Int) -> some View {
        HStack(alignment: .top, spacing: 0) {
            leadingSpacer

            ForEach(keys, id: \.self) { key in
                dataCell(for: key, at: rowIndex)
            }

            trailingSpacer
        }
        .fixedSize(horizontal: false, vertical: true)
    }

    @ViewBuilder
    private func dataCell(for key: String, at rowIndex: Int) -> some View {
        let values = table[key] ?? []
        let value = rowIndex < values.count ? values[rowIndex] : "-"

        ZStack {
            Rectangle()
                .fill(OGColors.backgroundBackground0.color)
                .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)

            Text(value)
                .font(for: .copyMRegular)
                .foregroundColor(OGColors.textOnLight.color)
                .multilineTextAlignment(.center)
                .padding(.vertical, UILayoutConstants.SizeAdvisorView.tableCellVerticalPadding)
                .padding(.horizontal, UILayoutConstants.SizeAdvisorView.tableCellHorizontalPadding)
                .frame(width: UILayoutConstants.SizeAdvisorView.columnWidth)
        }
        .overlay(
            Rectangle()
                .stroke(OGColors.backgroundBackground20.color, lineWidth: 1)
        )
    }

    private var leadingSpacer: some View {
        Spacer()
            .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
    }

    private var trailingSpacer: some View {
        Spacer()
            .frame(width: UILayoutConstants.SizeAdvisorView.horizontalScreenPadding)
    }
}

struct SizeDetailsHeader: View {
    let index: String
    let text: String
    
    var body: some View {
        HStack(alignment: .center) {
            ZStack {
                Circle()
                    .fill(OGColors.accentSuccess.color)
                    .frame(width: UILayoutConstants.SizeAdvisorView.circleSize, height: UILayoutConstants.SizeAdvisorView.circleSize)

                Text(index)
                    .font(for: .caption)
                    .foregroundColor(OGColors.textOnPrimary.color)
            }
            .accessibilityHidden(true)

            Text(text)
                .font(for: .headlineLEmphasized)
                .foregroundColor(OGColors.textOnLight.color)
                .padding(.leading, UILayoutConstants.SizeAdvisorView.itemSpacing)
            
            Spacer()
        }
    }
}

// MARK: - UILayoutConstants.SizeAdvisorView

extension UILayoutConstants {
    enum SizeAdvisorView {
        static let horizontalScreenPadding: CGFloat = 16
        static let headlinePadding: CGFloat = 24
        static let sectionSpacing: CGFloat = 32
        static let detailsSpacing: CGFloat = 16
        static let itemSpacing: CGFloat = 8
        static let tipCardPadding: CGFloat = 16
        static let tipCardCornerRadius: CGFloat = 12
        static let columnWidth: CGFloat = 90
        static let tableCellVerticalPadding: CGFloat = 12
        static let tableCellHorizontalPadding: CGFloat = 8
        static let tableVerticalPadding: CGFloat = 16
        static let circleSize: CGFloat = 32
        static let imageAspectRatio: CGFloat = 16/9
        static let minCellHeight: CGFloat = 44
    }
}
