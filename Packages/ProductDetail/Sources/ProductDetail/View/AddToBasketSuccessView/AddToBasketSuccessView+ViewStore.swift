import Combine
import <PERSON>GAppKitSDK
import OGDIService
import OGDomainStore
import OGFeatureAdapter
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore

extension AddToBasketSuccessView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(productId: String, screenId: String? = nil)
    -> Store {
    ProductDetailContainer.shared.addToBasketSuccessDomainStore.register {
      OGDomainStoreFactory.make(productId: productId, screenId: screenId)
    }

    return Store(
      initialState: .initial,
      reducer: AddToBasketSuccessView.Reducer.reduce,
      middleware: AddToBasketSuccessView.Middleware(),
      connector: AddToBasketSuccessView.Connector()
    )
  }

  enum Event: OGViewEvent {
    case dismiss
    case goToBasket
    case trackScreenView
    /// private
    case _received([OGAddToBasketSuccessComponent])
    case _receivedButtons(AddToBasketSuccessButtons)
  }

  public struct ViewState: OGViewState {
    private(set) var components: [OGAddToBasketSuccessComponent] = []
    private(set) var basketPath: String = ""
    private(set) var hasShowBasketButton: Bool = false
    private(set) var hasContinueShoppingButton: Bool = false
    private(set) var continueShoppingUrl: String?
    init(
      components: [OGAddToBasketSuccessComponent] = [],
      basketPath: String = "",
      hasShowBasketButton: Bool = false,
      hasContinueShoppingButton: Bool = false,
      continueShoppingUrl: String? = nil
    ) {
      self.components = components
      self.basketPath = basketPath
      self.hasShowBasketButton = hasShowBasketButton
      self.hasContinueShoppingButton = hasContinueShoppingButton
      self.continueShoppingUrl = continueShoppingUrl
    }

    public static var initial = ViewState()
    mutating func update(
      components: [OGAddToBasketSuccessComponent]? = nil
    ) {
      if self.components.isEmpty {
        self.components = components ?? self.components
      } else if self.components.count != components?.count {
        self.components = components ?? self.components
      } else if !(components?.dynamicYieldRecommendations.hasEqualContent(to: self.components.dynamicYieldRecommendations) ?? false) {
        self.components = components ?? self.components
      } else if !(components?.gkAirRecommendations.hasEqualContent(to: self.components.gkAirRecommendations) ?? false) {
        self.components = components ?? self.components
      }
    }

    mutating func update(
      buttons: AddToBasketSuccessButtons? = nil
    ) {
      basketPath = buttons?.showBasket?.config.basketUrl ?? basketPath
      continueShoppingUrl = buttons?.continueShopping?.config.continueShoppingUrl ?? continueShoppingUrl
      hasShowBasketButton = buttons?.showBasket != nil
      hasContinueShoppingButton = buttons?.continueShopping != nil
    }
  }

  enum Reducer {
    static func reduce(
      _ state: inout AddToBasketSuccessView.ViewState,
      with event: AddToBasketSuccessView.Event
    ) {
      switch event {
      case let ._received(components):
        state.update(components: components)
      case let ._receivedButtons(buttons):
        state.update(buttons: buttons)
      case .dismiss, .goToBasket, .trackScreenView:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    let router: OGRoutePublishing
    let baseUrl: OGBaseUrlFeatureAdaptable
    let tracker: OGTrackerProtocol
    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      baseUrl: OGBaseUrlFeatureAdaptable = OGBaseUrlFeatureAdapterContainer.shared.baseUrl(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.router = router
      self.baseUrl = baseUrl
      self.tracker = tracker
    }

    func callAsFunction(
      event: AddToBasketSuccessView.Event,
      for state: AddToBasketSuccessView.ViewState
    ) async -> AddToBasketSuccessView.Event? {
      switch event {
      case .dismiss:
        router.dismiss(route: .addToBasketSuccess)
        if let continueShoppingUrl = state.continueShoppingUrl {
          let url = baseUrl.urlRelativeToWebUrl(forUrlPath: continueShoppingUrl)
          router.send(OGRoute(url: url))
        }
        return nil
      case .goToBasket:
        let url = baseUrl.urlRelativeToWebUrl(forUrlPath: state.basketPath)
        router.dismiss(route: .addToBasketSuccess)
        router.send(OGRoute(url: url))
        return nil
      case .trackScreenView:
        tracker.multiplatformTrack(event: View.ScreenProductDetailConfirmation())
        return nil
      case ._received, ._receivedButtons:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    let addToBasketSuccessStore: AddToBasketSuccessStore
    init(
      addToBasketSuccessStore: AddToBasketSuccessStore = ProductDetailContainer.shared.addToBasketSuccessDomainStore()
    ) {
      self.addToBasketSuccessStore = addToBasketSuccessStore
    }

    func configure(
      dispatch: @escaping (AddToBasketSuccessView.Event) async -> Void
    ) async {
      await addToBasketSuccessStore
        .statePublisher
        .removeDuplicates()
        .map(\.components)
        .compactMap { $0 }
        .sink { components in
          Task {
            await dispatch(._received(components))
          }
        }
        .store(in: &cancellables)

      await addToBasketSuccessStore
        .statePublisher
        .removeDuplicates()
        .map(\.addToBasketSuccessButtons)
        .compactMap { $0 }
        .sink { addToBasketSuccessButtons in
          Task {
            await dispatch(._receivedButtons(addToBasketSuccessButtons))
          }
        }
        .store(in: &cancellables)
    }
  }
}

// MARK: - Product

extension AddedProduct.Content {
  var formattedPrice: String {
    let priceFormatter = ProductDetailContainer.shared.priceFormatter()
    return priceFormatter.format(
      Int(price.value),
      currencyCode: price.currency
    )
  }

  var formattedOriginalPrice: String? {
    guard let oldValue = price.oldValue else { return nil }
    let priceFormatter = ProductDetailContainer.shared.priceFormatter()
    return priceFormatter.format(
      Int(truncating: oldValue),
      currencyCode: price.currency
    )
  }
}
