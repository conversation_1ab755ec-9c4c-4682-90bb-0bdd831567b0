import OGCore
import OGIdentifier
import <PERSON>GRouter
import SwiftUI

// MARK: - AddToBasketSuccessViewDestinationProvider

public struct AddToBasketSuccessViewDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier = OGIdentifier.addToBasketSuccess

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    if let routeData: AddToBasketSuccessRouteData = route.getData() {
      AddToBasketSuccessView(productId: routeData.productId, screenId: routeData.screenId)
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  public static let addToBasketSuccess = #identifier("addToBasketSuccess")
}

extension OGRoute {
  public static let addToBasketSuccess = OGRoute(OGIdentifier.addToBasketSuccess.value)
}

// MARK: - AddToBasketSuccessRouteData

struct AddToBasketSuccessRouteData: Codable {
  let productId: String
  let screenId: String?
  init(productId: String, screenId: String? = nil) {
    self.productId = productId
    self.screenId = screenId
  }
}
