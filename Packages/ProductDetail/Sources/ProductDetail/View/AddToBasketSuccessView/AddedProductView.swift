import OGAppKitSDK
import SwiftUI
import UICatalog

struct AddedProductView: SwiftUI.View {
  let product: AddedProduct

  var body: some SwiftUI.View {
    HStack(alignment: .top, spacing: .zero) {
      if product.isLoading {
        loadingImage
      } else {
        image
      }
      VStack(alignment: .leading, spacing: .zero) {
        title
        selectedDimensionValues
        price
      }
      Spacer()
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)

    ComponentDivider()
      .padding(.leading, UILayoutConstants.Default.padding2x)
      .padding(.top, UILayoutConstants.Default.padding2x)
  }

  var price: some SwiftUI.View {
    Text(product.content.formattedPrice)
      .font(for: .priceMEmphasized)
      .foregroundColor(product.content.price.oldValue != nil ? OGColors.textSale.color : OGColors.textOnLight.color)
      .shimmering(active: product.isLoading)
      .padding(.top, UILayoutConstants.Default.padding)
  }

  var selectedDimensionValues: some SwiftUI.View {
    ForEach(product.content.selectedDimensionValues, id: \.self) { selectedDimensionValue in
      AdaptiveHStack(alignment: .firstTextBaseline) {
        Text("\(selectedDimensionValue.dimensionName):")
          .font(for: .copyMRegular)
          .foregroundColor(OGColors.textOnLight.color)
          .shimmering(active: product.isLoading)
        Text(selectedDimensionValue.value)
          .font(for: .titleS)
          .foregroundColor(OGColors.textOnLight.color)
          .shimmering(active: product.isLoading)
        Spacer()
      }
    }
  }

  @ViewBuilder var title: some SwiftUI.View {
    if let brandName = product.content.brandName {
      Text(brandName)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .shimmering(active: product.isLoading)
    }
    Text(product.content.title)
      .font(for: .titleM)
      .foregroundColor(OGColors.textOnLight.color)
      .padding(.bottom, UILayoutConstants.Default.padding)
      .shimmering(active: product.isLoading)
  }

  var loadingImage: some SwiftUI.View {
    Rectangle()
      .fill(.clear)
      .frame(
        width: UILayoutConstants.AddToBasketSuccessView.imageWidth,
        height: UILayoutConstants.AddToBasketSuccessView.imageHeight
      )
      .background(OGColors.backgroundBackground20.color)
      .shimmering(cornerRadius: .zero)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
  }

  var image: some SwiftUI.View {
    AsyncCachedImage(
      url: URL(string: product.content.image.url),
      maxWidth: UILayoutConstants.AddToBasketSuccessView.imageWidth,
      maxHeight: UILayoutConstants.AddToBasketSuccessView.imageHeight,
      targetAspectRatio: UILayoutConstants.AddToBasketSuccessView.aspectRatio,
      content: { image in
        image
          .resizable()
          .aspectRatio(UILayoutConstants.AddToBasketSuccessView.aspectRatio, contentMode: .fill)
      },
      placeholder: { loadingState in
        VStack {
          if case .failure = loadingState {
            OGImages.icon24x24PlaceholderImg.image
          } else {
            Rectangle()
              .fill(.clear)
              .shimmering(cornerRadius: .zero)
          }
        }
      }
    )
    .frame(
      width: UILayoutConstants.AddToBasketSuccessView.imageWidth,
      height: UILayoutConstants.AddToBasketSuccessView.imageHeight
    )
    .background(OGColors.backgroundBackground20.color)
    .padding(.trailing, UILayoutConstants.Default.padding2x)
  }
}
