import OGAppKitSDK
extension OGAppKitSDK.SizeTableData {
  var toSizeTableData: SizeTableData
  {
    SizeTableData(
      tables: self.tables.map(\.toTableData),
      detailsHeadline: self.detailsHeadline,
      detailsDescription: self.detailsDescription,
      detailsItems: self.detailsItems.map(\.toDetailsItem),
      detailsImageUrl: self.detailsImageUrl,
      tipText: self.tipText
    )
  }
}
extension OGAppKitSDK.SizeTableDetailsItem {
  var toSizeTableDetailsItem: SizeTableDetailsItem {
    SizeTableDetailsItem(title: self.title, description: self.description_)
  }
  var toDetailsItem: DetailsItem {
    DetailsItem(title: self.title, description: self.description_)
  }
}

extension OGAppKitSDK.SizeTable {
  var toTableData: TableData {
    TableData(headline: self.headline,
              tableDetailsItems: self.tableDetailsItems?.map(\.toSizeTableDetailsItem),
              table: self.table)
  }
}

// MARK: - Data Models
struct SizeTableData: Codable, Equatable {
    let tables: [TableData]
    let detailsHeadline: String?
    let detailsDescription: String?
    let detailsItems: [DetailsItem]
    let detailsImageUrl: String?
    let tipText: String?
}

struct TableData: Codable,Equatable {
    let headline: String?
    let tableDetailsItems: [SizeTableDetailsItem]?
    let table: [String: [String]]
}

struct SizeTableDetailsItem: Codable, Equatable {
    let title: String
    let description: String
}

struct DetailsItem: Codable, Equatable {
    let title: String
    let description: String
}
