import Combine
import Foundation
import <PERSON>GAppKitSDK
import OGL10n
import <PERSON><PERSON><PERSON>er
import OGTracker
import OGViewStore

extension GalleryView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore(
    gallery: ProductGallery,
    indexOfCurrentImage: Int = 0,
    route: OGRoute? = nil,
    isFullscreen: Bool = false
  ) -> Store {
    let indexOfCurrentImagePublisher = CurrentValueSubject<Decodable, Never>(indexOfCurrentImage)

    return GalleryView.Store(
      initialState: ViewState(
        urls: gallery.content.urls,
        flags: gallery.content.flags,
        indexOfCurrentImage: indexOfCurrentImage,
        isWishlisted: gallery.content.isWishlisted,
        productIdForWishlisting: gallery.content.productIdForWishlisting,
        isLoading: gallery.isLoading,
        isFullscreen: isFullscreen
      ),
      reducer: GalleryView.ViewState.Reducer.reduce,
      middleware: GalleryView.ViewState.Middleware(indexOfCurrentImagePublisher: route?.publisher ?? indexOfCurrentImagePublisher),
      connector: GalleryView.ViewState.Connector(gallery: gallery, indexOfCurrentImagePublisher: indexOfCurrentImagePublisher)
    )
  }
}

extension GalleryView.ViewState {
  var accessibilityValue: String {
    if numberOfCurrentImage == 1 {
      let flagsNames = flags.map(\.name).joined(separator: " ")
      return "\(flagsNames) \(ogL10n.ProductDetail.Gallery.Count.Accessibility(numberOfCurrentImage: String(numberOfCurrentImage), numberOfImages: String(numberOfImages)))"

    } else {
      return ogL10n.ProductDetail.Gallery.Count.Accessibility(numberOfCurrentImage: String(numberOfCurrentImage), numberOfImages: String(numberOfImages))
    }
  }

  var imageCountCurrentLabel: String {
    string(from: numberOfCurrentImage)
  }

  var imageCountMaxLabel: String {
    string(from: numberOfImages)
  }

  private func string(from: Int) -> String {
    numberFormatter.string(from: NSNumber(value: from)) ?? ""
  }

  private var numberFormatter: NumberFormatter {
    let numberFormatter = NumberFormatter()
    numberFormatter.minimumIntegerDigits = 2
    return numberFormatter
  }

  var showsNextButton: Bool {
    indexOfCurrentImage < (numberOfImages - 1)
  }

  var showsPreviousButton: Bool {
    indexOfCurrentImage > 0
  }
}

extension GalleryView {
  enum Event: OGViewEvent {
    case indexOfCurrentImage(Int)
    case moveToNextImage
    case moveToPreviousImage
    case tapedImage
    case dismiss
    case onSwipe(endLocation: CGPoint, startLocation: CGPoint)
    case currentScale(CGFloat)
    case prefetchNextImageFor(Int)
    case trackScreenView
    /// private
    case _received(ProductGallery)
    case _trackInteraction
    case _didTrackInteraction
  }

  public struct ViewState: OGViewState {
    private(set) var urls: [URL]
    private(set) var flags: [OGFlag]

    private(set) var numberOfCurrentImage: Int
    private(set) var shouldShowImageCount: Bool
    private(set) var numberOfImages: Int
    private(set) var indexOfCurrentImage: Int
    private(set) var currentScale: CGFloat
    private(set) var swipeDistanceToDismiss: CGFloat
    private(set) var isWishlisted: Bool
    private(set) var productIdForWishlisting: String?
    private(set) var isLoading: Bool
    private(set) var isFullscreen: Bool
    private(set) var didTrackInteraction: Bool
    public init(
      urls: [URL] = [URL](),
      flags: [Flag] = [Flag](),
      indexOfCurrentImage: Int = 0,
      numberOfImages: Int = 0,
      shouldShowImageCount: Bool = false,
      currentScale: CGFloat = 1,
      swipeDistanceToDismiss: CGFloat = 150,
      isWishlisted: Bool = false,
      productIdForWishlisting: String? = nil,
      isLoading: Bool = true,
      isFullscreen: Bool = false,
      didTrackInteraction: Bool = false
    ) {
      self.urls = urls
      self.flags = flags.map { $0.flag }
      self.shouldShowImageCount = shouldShowImageCount
      self.indexOfCurrentImage = indexOfCurrentImage
      self.numberOfCurrentImage = indexOfCurrentImage + 1
      self.numberOfImages = numberOfImages
      self.currentScale = currentScale
      self.swipeDistanceToDismiss = swipeDistanceToDismiss
      self.isWishlisted = isWishlisted
      self.productIdForWishlisting = productIdForWishlisting
      self.isLoading = isLoading
      self.isFullscreen = isFullscreen
      self.didTrackInteraction = didTrackInteraction
    }

    mutating func update(
      urls: [URL],
      flags: [Flag],
      isWishlisted: Bool,
      productIdForWishlisting: String,
      isLoading: Bool
    ) {
      self.urls = urls
      self.flags = flags.map { $0.flag }
      numberOfImages = urls.count
      shouldShowImageCount = numberOfImages > 0
      self.isWishlisted = isWishlisted
      self.productIdForWishlisting = productIdForWishlisting
      self.isLoading = isLoading
    }

    mutating func update(indexOfCurrentImage: Int) {
      numberOfCurrentImage = indexOfCurrentImage + 1
      self.indexOfCurrentImage = indexOfCurrentImage
    }

    mutating func update(shouldShowImageCount: Bool) {
      self.shouldShowImageCount = shouldShowImageCount
    }

    mutating func update(didTrackInteraction: Bool) {
      self.didTrackInteraction = didTrackInteraction
    }

    mutating func update(currentScale: CGFloat) {
      shouldShowImageCount = currentScale <= 1
      self.currentScale = currentScale
    }

    public static var initial = ViewState()
  }
}

extension GalleryView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout GalleryView.ViewState,
      with event: GalleryView.Event
    ) {
      switch event {
      case .dismiss, .onSwipe:
        break
      case let ._received(productGallery):
        state.update(
          urls: productGallery.content.urls,
          flags: productGallery.content.flags,
          isWishlisted: productGallery.content.isWishlisted,
          productIdForWishlisting: productGallery.content.productIdForWishlisting,
          isLoading: productGallery.isLoading
        )
      case let .indexOfCurrentImage(index):
        if index != state.indexOfCurrentImage {
          state.currentScale = 1
        }
        state.update(indexOfCurrentImage: index)
      case let .currentScale(scale):
        state.update(currentScale: scale)
      case ._didTrackInteraction:
        state.update(didTrackInteraction: true)
      case ._trackInteraction, .moveToNextImage, .moveToPreviousImage, .prefetchNextImageFor, .tapedImage, .trackScreenView: break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing
    private let indexOfCurrentImagePublisher: CurrentValueSubject<Decodable, Never>?
    private let imageDownloader: AsyncCachedImageDownloadable
    private let tracker: OGTrackerProtocol
    init(
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      indexOfCurrentImagePublisher: CurrentValueSubject<Decodable, Never>?,
      imageDownloader: AsyncCachedImageDownloadable = AsyncCachedImageDownloader(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.router = router
      self.indexOfCurrentImagePublisher = indexOfCurrentImagePublisher
      self.imageDownloader = imageDownloader
      self.tracker = tracker
    }

    func callAsFunction(
      event: GalleryView.Event,
      for state: GalleryView.ViewState
    ) async
      -> GalleryView.Event? {
      switch event {
      case .tapedImage:
        let model = Gallery(
          images: state.urls.map(\.absoluteString),
          selectedIndex: state.indexOfCurrentImage
        )
        var galleryViewRoute = OGRoute(OGRoute.galleryView, data: model)
        if let indexOfCurrentImagePublisher {
          galleryViewRoute.set(publisher: indexOfCurrentImagePublisher)
        }
        router.send(galleryViewRoute)
        return ._trackInteraction
      case .dismiss:
        router.dismiss(route: .galleryView)
        return nil
      case let .indexOfCurrentImage(index):
        if state.isFullscreen {
          indexOfCurrentImagePublisher?.send(index)
        }
        if state.indexOfCurrentImage != 0 {
          return ._trackInteraction
        } else {
          return nil
        }
      case let .onSwipe(endLocation: endLocation, startLocation: startLocation):
        if endLocation.y - startLocation.y > state.swipeDistanceToDismiss, state.currentScale == 1 {
          return .dismiss
        } else {
          return nil
        }
      case .moveToNextImage:
        return .indexOfCurrentImage(state.indexOfCurrentImage + 1)
      case .moveToPreviousImage:
        return .indexOfCurrentImage(state.indexOfCurrentImage - 1)
      case let .prefetchNextImageFor(index):
        guard let url = state.urls[safe: index + 1] else {
          return nil
        }
        Task.detached {
          try? await imageDownloader.downloadImage(url: url)
        }
        return nil
      case ._trackInteraction:
        guard !state.isFullscreen, !state.didTrackInteraction else { return nil }
        tracker.multiplatformTrack(event: Interaction.ProductDetailUseMediaGallery())
        return ._didTrackInteraction
      case .trackScreenView:
        guard state.isFullscreen else { return nil }
        tracker.multiplatformTrack(event: View.ScreenProductDetailGallery())
        return nil
      case ._didTrackInteraction, ._received, .currentScale:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    private let gallery: ProductGallery
    private let indexOfCurrentImagePublisher: CurrentValueSubject<Decodable, Never>?
    init(gallery: ProductGallery, indexOfCurrentImagePublisher: CurrentValueSubject<Decodable, Never>? = nil) {
      self.gallery = gallery
      self.indexOfCurrentImagePublisher = indexOfCurrentImagePublisher
    }

    func configure(
      dispatch: @escaping (GalleryView.Event) async -> Void
    ) async {
      indexOfCurrentImagePublisher?
        .compactMap { $0 as? Int }
        .removeDuplicates()
        .sink { index in
          Task {
            await dispatch(.indexOfCurrentImage(index))
          }
        }
        .store(in: &cancellables)

      await dispatch(._received(gallery))
    }
  }

  struct Gallery: Codable {
    let images: [String]
    let selectedIndex: Int

    init(images: [String], selectedIndex: Int) {
      self.images = images
      self.selectedIndex = selectedIndex
    }
  }
}

extension ProductGallery.Content {
  var urls: [URL] {
    images.compactMap { URL(string: $0.url) }
  }
}
