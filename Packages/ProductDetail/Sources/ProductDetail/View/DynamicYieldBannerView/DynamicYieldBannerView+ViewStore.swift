
import AppCore
import Combine
import Foundation
import OGAppKitSDK
import OGCore
import <PERSON><PERSON>outer
import OGTracker
import OGViewStore
import UIKit.UIPasteboard
extension DynamicYieldBannerView {
  typealias Store = OGViewStore<ViewState, Event>

  @MainActor
  static func makeStore(
    dynamicYieldBanner: PromoBanner
  ) -> Store {
    Store(
      initialState: ViewState(
        text: dynamicYieldBanner.content.text,
        promoCode: dynamicYieldBanner.content.promoCode ?? "",
        showsInfo: dynamicYieldBanner.content.infoText != nil,
        infoText: dynamicYieldBanner.content.infoText ?? ""
      ),
      reducer: ViewState.Reducer.reduce,
      middleware: ViewState.Middleware(),
      connector: ViewState.Connector(dynamicYieldBanner: dynamicYieldBanner)
    )
  }

  struct ViewState: OGViewState {
    private(set) var text: String = ""
    private(set) var didCopyCode: Bool = false
    private(set) var showsInfo: Bool = false
    private(set) var promoCode: String = ""
    private(set) var infoText: String
    private var clickTrackingEvent: Interaction.ProductDetailSelectPromotion?
    private var viewTrackingEvent: View.ProductDetailPromotion?
    private var didTrackView: Bool = false
    init(
      didCopyCode: Bool = false,
      text: String = "",
      promoCode: String = "",
      showsInfo: Bool = false,
      infoText: String = ""
    ) {
      self.didCopyCode = didCopyCode
      self.text = text
      self.promoCode = promoCode
      self.showsInfo = showsInfo
      self.infoText = infoText
    }

    mutating func update(dynamicYieldBanner: PromoBanner) {
      text = dynamicYieldBanner.content.text
      promoCode = dynamicYieldBanner.content.promoCode ?? ""
      infoText = dynamicYieldBanner.content.infoText ?? ""
      showsInfo = dynamicYieldBanner.content.infoText != nil
      clickTrackingEvent = dynamicYieldBanner.content.trackingEvents?.click
      viewTrackingEvent = dynamicYieldBanner.content.trackingEvents?.view
    }

    mutating func update(didCopyCode: Bool? = nil, didTrackView: Bool? = nil) {
      self.didCopyCode = didCopyCode ?? self.didCopyCode
      self.didTrackView = didTrackView ?? self.didTrackView
    }

    static var initial = ViewState()
  }

  enum Event: OGViewEvent, Equatable {
    static func == (lhs: DynamicYieldBannerView.Event, rhs: DynamicYieldBannerView.Event) -> Bool {
      switch (lhs, rhs) {
      case (.onCopyCode, .onCopyCode), (.trackView, .trackView), (._didTrackView, ._didTrackView), (._wait, ._wait), (._reset, ._reset):
        return true
      case let (._setDynamicYieldBanner(lhsBanner), ._setDynamicYieldBanner(rhsBanner)):
        return lhsBanner.content == rhsBanner.content
      case (. _trackInteraction, . _trackInteraction):
        return true
      default:
        return false
      }
    }
    
    case onCopyCode
    case trackView
    /// Private
    case _setDynamicYieldBanner(PromoBanner)
    case _trackInteraction
    case _didTrackView
    case _wait
    case _reset
  }
}

extension DynamicYieldBannerView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout DynamicYieldBannerView.ViewState,
      with event: DynamicYieldBannerView.Event
    ) {
      switch event {
      case let ._setDynamicYieldBanner(dynamicYieldBanner):
        state.update(dynamicYieldBanner: dynamicYieldBanner)
      case .onCopyCode:
        state.update(didCopyCode: true)
      case ._didTrackView:
        state.update(didTrackView: true)
      case ._reset:
        state.update(didCopyCode: false)
      case ._trackInteraction, ._wait, .trackView: break
      }
    }
  }

  final class Middleware: OGViewStoreMiddleware {
    private var pasteboard: PasteboardServing
    private var tracker: OGTrackerProtocol
    private let resetWaitDuration: UInt64
    init(
      pasteboard: PasteboardServing = UIPasteboard.general,
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker(),
      resetWaitDuration: UInt64 = 5_000_000_000
    ) {
      self.pasteboard = pasteboard
      self.tracker = tracker
      self.resetWaitDuration = resetWaitDuration
    }

    func callAsFunction(
      event: DynamicYieldBannerView.Event,
      for state: DynamicYieldBannerView.ViewState
    ) async -> DynamicYieldBannerView.Event? {
      switch event {
      case ._setDynamicYieldBanner:
        return nil
      case .onCopyCode:
        pasteboard.string = state.promoCode
        return ._trackInteraction
      case ._trackInteraction:
        guard let clickTrackingEvent = state.clickTrackingEvent else { return nil }
        tracker.multiplatformTrack(event: clickTrackingEvent)
        return ._wait
      case ._wait:
        try? await Task.sleep(nanoseconds: resetWaitDuration)
        return ._reset
      case .trackView:
        guard let viewTrackingEvent = state.viewTrackingEvent, !state.didTrackView else { return nil }
        tracker.multiplatformTrack(event: viewTrackingEvent)
        return ._didTrackView
      case ._didTrackView, ._reset:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    let dynamicYieldBanner: PromoBanner

    init(
      dynamicYieldBanner: PromoBanner
    ) {
      self.dynamicYieldBanner = dynamicYieldBanner
    }

    func configure(
      dispatch: @escaping (DynamicYieldBannerView.Event) async -> Void
    ) async {
      await dispatch(._setDynamicYieldBanner(dynamicYieldBanner))
    }
  }
}
