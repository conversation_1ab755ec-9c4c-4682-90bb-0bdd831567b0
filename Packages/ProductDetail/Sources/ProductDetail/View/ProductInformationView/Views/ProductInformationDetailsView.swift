import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductInformationDetailsView

struct ProductInformationDetailsView: SwiftUI.View {
  let productDetails: ProductInformation.Details
  let isLoading: Bool
  var body: some SwiftUI.View {
    DropdownView(isLoading: isLoading, name: ogL10n.ProductDetail.Information.Details.Title) {
      Text(ogL10n.ProductDetail.Information.Details.Title)
        .font(for: .copyL)
        .foregroundColor(OGColors.textOnLight.color)
    } contentView: {
      VStack(alignment: .leading, spacing: .zero) {
        ForEach(Array(productDetails.attributesTable.sections.enumerated()), id: \.offset) { sectionsIndex, section in
          if !section.title.isEmpty {
            sectionTitle(section.title)
              .padding(.trailing, UILayoutConstants.Default.padding2x)
              .padding(.bottom, UILayoutConstants.Default.padding2x)
          }
          ForEach(Array(section.entries.enumerated()), id: \.offset) { index, entry in
            entryContent(entry)
              .padding(.trailing, UILayoutConstants.Default.padding2x)
              .padding(.bottom, UILayoutConstants.Default.padding2x)
            let isLastSectionLastEntry = (sectionsIndex == productDetails.attributesTable.sections.count - 1) &&
              (index == section.entries.count - 1)

            if !isLastSectionLastEntry {
              ComponentDivider()
                .padding(.bottom, UILayoutConstants.Default.padding2x)
            }
          }
        }
      }
      .multilineTextAlignment(.leading)
    }
  }

  private func sectionTitle(_ title: String) -> some SwiftUI.View {
    Text(title)
      .font(for: .titleM)
      .foregroundColor(OGColors.textOnLight.color)
      .frame(
        maxWidth: .infinity,
        minHeight: UILayoutConstants.ProductInformationDetailsView.sectionMinHeight,
        alignment: .leading
      )
  }

  private func entryContent(_ entry: Information.AttributesTableSectionEntry) -> some SwiftUI.View {
    VStack(alignment: .leading, spacing: .zero) {
      Text(entry.key)
        .font(for: .copyMEmphasized)
        .foregroundColor(OGColors.textOnLight.color)
        .padding(.bottom, UILayoutConstants.ProductInformationDetailsView.entryKeyPaddingBottom)
      ForEach(entry.values, id: \.self) { line in
        HStack(alignment: .top) {
          Text("•")
            .accessibilityHidden(true)
          Text(line.trimmingCharacters(in: .whitespaces))
        }
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .frame(maxWidth: .infinity, alignment: .leading)
      }
      .accessibilityElement(children: .combine)
    }

    .multilineTextAlignment(.leading)
    .frame(minHeight: UILayoutConstants.ProductInformationDetailsView.sectionMinHeight)
  }
}

// MARK: - UILayoutConstants.ProductInformationDetailsView

extension UILayoutConstants {
  enum ProductInformationDetailsView {
    static let sectionMinHeight: CGFloat = 40
    static var entryKeyPaddingBottom: CGFloat = 2
  }
}
