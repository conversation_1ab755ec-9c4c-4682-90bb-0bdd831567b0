enum MonitoredView: <PERSON><PERSON>le {
  case galleryWishlistIcon
  case productAvailability
  case productColorDimension
  case productDimensions
  case productGallery
  case productHeader
  case productInformation
  case productPrice
  case productRating
  case productTitle
  case addToBasketButton
  case productColor
  case productReviews
  case recentlyViewedRecommendations
  case staticProductRecommendations
  case dynamicYieldRecommendations
  case shopTheLookRecommendations
  case shopUsps
  case productVariant
  case dynamicYieldPromoBanner
  case moreFromTheSeriesRecommendations
  case gkAirRecommendations
  case contentfulPromoBanner
}
