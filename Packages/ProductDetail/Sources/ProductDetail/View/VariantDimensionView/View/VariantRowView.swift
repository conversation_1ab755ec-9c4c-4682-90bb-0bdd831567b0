import SwiftUI
import UICatalog

// MARK: - VariantRowView

struct VariantRowView: View {
  let variant: ProductDimensionsVariant
  let minVariantNameWidth: CGFloat?
  let onSelect: (String, Bool) -> Void

  init(variant: ProductDimensionsVariant, minVariantNameWidth: CGFloat? = nil, onSelect: @escaping (String, Bool) -> Void) {
    self.variant = variant
    self.minVariantNameWidth = minVariantNameWidth
    self.onSelect = onSelect
  }

  var body: some View {
    HStack(spacing: UILayoutConstants.VariantSelectionView.rowSpacing) {
      SelectionIndicatorView(
        isSelected: variant.isSelected
      )
      variantName
      variantInfo
      Spacer()
      variantPrice
    }
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(variant.isSelected ? .isSelected : .isButton)
    .padding(UILayoutConstants.VariantSelectionView.rowPadding)
    .contentShape(Rectangle())
    .onTapGesture {
      onSelect(variant.productId, variant.isAvailable)
    }
  }

  private var variantName: some View {
    Text(variant.name)
      .font(for: OGFonts.copyMRegular)
      .foregroundColor(OGColors.textOnLight.color)
      .frame(minWidth: minVariantNameWidth, alignment: .leading)
      .background(
        GeometryReader { geometry in
          Color.clear
            .preference(
              key: VariantNameWidthPreferenceKey.self,
              value: geometry.size.width
            )
        }
      )
  }

  private var variantInfo: some View {
    Text(variant.availabilityInfo)
      .font(for: OGFonts.titleS)
      .foregroundColor(Color(variant.availabilityColorName))
  }

  private var variantPrice: some View {
    Text(variant.formattedPrice)
      .font(for: OGFonts.priceSEmphasized)
      .foregroundColor(variant.hasDiscount ? OGColors.textSale.color : OGColors.textOnLight.color)
  }
}

// MARK: - VariantNameWidthPreferenceKey

struct VariantNameWidthPreferenceKey: PreferenceKey {
  static var defaultValue: CGFloat = 0

  static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
    value = max(value, nextValue())
  }
}
