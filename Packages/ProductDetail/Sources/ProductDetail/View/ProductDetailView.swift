import OGNavigationBar
import OGRouter
import SwiftUI
import UICatalog

// MARK: - ProductDetailView

struct ProductDetailView: View {
  @StateObject private var viewStore: Self.Store
  @State private var orientation = UIInterfaceOrientation.unknown
  @State private var showsStickyBasketButton = false
  @State private var slowLoadingTimer: Timer?
  @State private var stickyBasketViewHeight: CGFloat = UILayoutConstants.StickyBasketView.height
  @Environment(\.c2aButtonCornerRadius) private var cornerRadius
  init(route: OGRoute) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(route: route))
  }

  var body: some View {
    Group {
      if viewStore.header != nil {
        OGNavigationBar(titleView: titleView, trailingView: trailingView) {
          content
            .frame(maxWidth: UILayoutConstants.ProductDetailView.maxWidth(for: orientation))
        }
        .onAppear {
          Task {
            await viewStore.dispatch(.updateBadgeCount)
          }
        }
      } else {
        OGNavigationBar {
          content
            .frame(maxWidth: UILayoutConstants.ProductDetailView.maxWidth(for: orientation))
        }
      }
    }
    .onRotate { newOrientation in
      orientation = newOrientation
    }
  }

  @ViewBuilder var content: some View {
    switch viewStore.state.loadingState {
    case .initial, .progress:
      EmptyView()
    case .failure:
      ErrorView(type: .defaultError) {
        Task {
          await viewStore.dispatch(.reload)
        }
      }
    case .success:
      ScrollViewMonitoringView(onVisibilityChange: visibilityChanged) {
        base
      }
      .scrollIsDisabled(viewStore.isLoading)
      .disabled(viewStore.isLoading)
      .overlay(alignment: .bottom) {
        stickyBasketButton
          .onChange(of: viewStore.showsStickyBasketButton) { newValue in
            withAnimation {
              showsStickyBasketButton = newValue
            }
          }
      }
      .onChange(of: viewStore.isLoading) { newValue in
        slowLoadingTimer(newValue)
      }
      .onAppear {
        slowLoadingTimer(viewStore.isLoading)
        Task {
          await viewStore.dispatch(.trackScreenView)
        }
      }
      .onDisappear {
        slowLoadingTimer(false)
      }
    }
  }

  @ViewBuilder private var base: some View {
    if #available(iOS 16.0, *) {
      let layout: AnyLayout = orientation.isLandscape || UIDevice.current.userInterfaceIdiom == .pad ? AnyLayout(CustomIndexFlowLayout(primaryIndex: viewStore.galleryIndex, maxFlowedItems: viewStore.maxFlowedItems)) : AnyLayout(VStackLayout(alignment: .leading, spacing: .zero))
      layout {
        ForEach(viewStore.components, id: \.self) { component in
          viewForComponent(component: component)
        }
        stickyBasketButtonSpacer
      }
    } else {
      VStack(alignment: .leading, spacing: .zero) {
        ForEach(viewStore.components, id: \.self) { component in
          viewForComponent(component: component)
        }
        stickyBasketButtonSpacer
      }
    }
  }

  // swiftlint:disable cyclomatic_complexity
  // swiftlint:disable function_body_length
  @ViewBuilder
  private func viewForComponent(component: OGProductDetailComponent) -> some View {
    switch component {
    case let .productGallery(gallery):
      GalleryView(gallery: gallery, indexOfCurrentImage: viewStore.indexOfCurrentImage) { index in
        Task {
          await viewStore.dispatch(.indexOfCurrentImage(index))
        }
      }
      .monitorVisibility(withID: component.toMonitoredView)
      .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
    case let .productColorDimension(colorDimension):
      ColorDimensionView(colorDimension: colorDimension, selectedProductId: { productId in
        Task {
          await viewStore.dispatch(.getDetailScreenForColor(id: productId))
        }
      })
      .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
      .monitorVisibility(withID: component.toMonitoredView)
    case let .productRating(rating):
      ProductRatingView(productRating: rating)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.bottom, UILayoutConstants.Default.padding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productPrice(price):
      PriceView(price: price)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productAvailability(availability):
      ProductAvailabilityView(availability: availability)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productColor(productColor):
      ColorNameView(name: productColor.content.colorName, isLoading: productColor.isLoading)
        .padding(.bottom, viewStore.hasVariantName ? UILayoutConstants.Default.padding : UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productVariant(productVariant):
      ProductVariantView(
        name: productVariant.content.variant.name,
        availability: productVariant.content.variant.availability.state,
        isLoading: productVariant.isLoading
      )
      .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
      .padding(.horizontal, UILayoutConstants.Default.padding2x)
      .monitorVisibility(withID: component.toMonitoredView)
    case let .productDimensions(productDimensions):
      ProductDimensionsView(productDimensions: productDimensions) {
        Task {
          await viewStore.dispatch(.openVariantSelection)
        }
      } didSelectSize: { productID in
        Task {
          await viewStore.dispatch(.getDetailScreenForSize(id: productID))
        }
      }
      .id(productDimensions.content.selectedID)
      .padding(.horizontal, UILayoutConstants.Default.padding2x)
      .padding(.bottom, UILayoutConstants.ProductDetailView.productDimensionsBottomPadding)
      .monitorVisibility(withID: component.toMonitoredView)
    case let .addToBasketButton(addToBasketButton):
      Group {
        if let contentAddToBasket = addToBasketButton.contentAddToBasket {
          BasketButtonOrNotifyMeView(state: .basket(
            productId: contentAddToBasket.productId,
            canAddToBasket: viewStore.canAddToBasket,
            isVoucher: viewStore.selectedVariant.isVoucher,
            customName: viewStore.selectedVariant.customName
          )
          ) {
            Task {
              await viewStore.dispatch(.openVariantSelection)
            }
          }
        }
        if let contentNotifyMe = addToBasketButton.contentNotifyMe {
          BasketButtonOrNotifyMeView(state: .notifyMe(url: contentNotifyMe.url))
        }
      }
      .shimmering(active: addToBasketButton.isLoading, cornerRadius: cornerRadius)
      .monitorVisibility(withID: component.toMonitoredView)
      .padding(.horizontal, UILayoutConstants.Default.padding2x)
      .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
    case let .shopUsps(shopUsps):
      ShopUspsView(shopUsps: shopUsps)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productInformation(productInformation):
      ProductInformationView(productInformation: productInformation)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productReviews(reviews):
      ProductReviewsView(productReviews: reviews)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .staticProductRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .recentlyViewedRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .dynamicYieldRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .shopTheLookRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .moreFromTheSeriesRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .gkAirRecommendations(recommendations):
      ProductRecommendationsView(recommendations: recommendations)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .productTitle(title):
      Text(title.content.title)
        .componentHeadline
        .shimmering(active: title.isLoading)
        .padding(.bottom, UILayoutConstants.ProductDetailView.mainComponentBottomPadding)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .monitorVisibility(withID: component.toMonitoredView)
    case .productHeader:
      EmptyView()
    case let .dynamicYieldPromoBanner(dynamicYieldBanner):
      DynamicYieldBannerView(dynamicYieldBanner: dynamicYieldBanner)
        .monitorVisibility(withID: component.toMonitoredView)
    case let .contentfulPromoBanner(contentfulPromoBanner):
      DynamicYieldBannerView(dynamicYieldBanner: contentfulPromoBanner)
        .monitorVisibility(withID: component.toMonitoredView)
    }
  }
}

// MARK: Header

extension ProductDetailView {
  @ViewBuilder private var titleView: some View {
    if let header = viewStore.header {
      ProductTitleView(header: header)
    }
  }

  @ViewBuilder private var trailingView: some View {
    if let header = viewStore.header, !header.isLoading {
      ProductTitleActionView(header: header, showsWishlistIcon: viewStore.showsHeaderWishlistIcon)
    }
  }
}

// MARK: StickyBasket

extension ProductDetailView {
  var showsStickyBasket: Bool {
    showsStickyBasketButton &&
      !orientation.isLandscape
  }

  @ViewBuilder private var stickyBasketButton: some View {
    if showsStickyBasket,
       let price = viewStore.stickyBasket?.price,
       let productId = viewStore.stickyBasket?.basket?.contentAddToBasket?.productId {
      StickyBasketButtonView(
        price: price,
        productId: productId,
        canAddToBasket: viewStore.canAddToBasket,
        openProductDimensions: {
          Task {
            await viewStore.dispatch(.openVariantSelection)
          }
        }
      )
      .background {
        GeometryReader { proxy in
          Color.clear
            .onChange(of: proxy.size) { size in
              stickyBasketViewHeight = size.height
            }
        }
      }
    }
  }

  @ViewBuilder private var stickyBasketButtonSpacer: some View {
    if showsStickyBasket {
      Spacer(minLength: stickyBasketViewHeight)
    }
  }
}

extension ProductDetailView {
  private func visibilityChanged(_ id: MonitoredView, isVisible: ItemVisibility) {
    Task {
      await viewStore.dispatch(.monitoredView(id, isVisible == .partially || isVisible == .fully))
    }
  }

  private func slowLoadingTimer(_ isLoading: Bool) {
    if isLoading {
      slowLoadingTimer?.invalidate()
      slowLoadingTimer = nil
      slowLoadingTimer = Timer.scheduledTimer(withTimeInterval: viewStore.slowLoading, repeats: false, block: { _ in
        Task {
          await viewStore.dispatch(.slowLoadingBanner(true))
        }
        slowLoadingTimer = nil
      })
    } else {
      slowLoadingTimer?.invalidate()
      slowLoadingTimer = nil
      Task {
        await viewStore.dispatch(.slowLoadingBanner(false))
      }
    }
  }
}

// MARK: - UILayoutConstants.ProductDetailView

extension UILayoutConstants {
  enum ProductDetailView {
    static let mainComponentBottomPadding: CGFloat = 24
    static let productDimensionsBottomPadding: CGFloat = 12
    static let iPadLandscapeMaxWidth: CGFloat = 768
    static func maxWidth(for orientation: UIInterfaceOrientation) -> CGFloat {
      UIDevice.current.userInterfaceIdiom == .pad && orientation.isLandscape ? iPadLandscapeMaxWidth : .infinity
    }
  }
}
