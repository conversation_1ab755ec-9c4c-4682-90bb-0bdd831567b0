import Combine
import OGAppKitSDK
import OGDomainStore
import <PERSON><PERSON>outer
import <PERSON>GViewStore
import SwiftUI
import UICatalog

extension WishlistButtonView {
  typealias Store = OGViewStore<ViewState, Event>
  @MainActor
  static func makeStore()
    -> Store {
    let wishlistStore = ProductDetailContainer.shared.wishlistStore()
    return Store(
      initialState: .initial,
      reducer: WishlistButtonView.ViewState.Reducer.reduce,
      middleware: WishlistButtonView.ViewState.Middleware(wishlistStore: wishlistStore),
      connector: WishlistButtonView.ViewState.Connector(wishlistStore: wishlistStore)
    )
  }
}

// MARK: - WishlistButtonView.ViewState

extension WishlistButtonView {
  public struct ViewState: OGViewState {
    public private(set) var isWishlisted: Bool = false
    private var wishlist: [String] = []
    private var productID: String?

    public static var initial = ViewState()
    mutating func update(
      productID: String? = nil,
      wishlist: [String]? = nil
    ) {
      self.wishlist = wishlist ?? self.wishlist
      self.productID = productID ?? self.productID
      if let productID = self.productID {
        isWishlisted = self.wishlist.contains(productID.components(separatedBy: "--").first ?? productID)
      }
    }

    mutating func update(isWishlisted: Bool) {
      self.isWishlisted = isWishlisted
    }
  }
}

// MARK: - WishlistButtonView.Event

extension WishlistButtonView {
  enum Event: OGViewEvent {
    case toggleWishlist(Bool)
    case setProductID(String?)

    /// Private Events
    case _removeFromWishlist(String)
    case _addToWishlist(String)
    case _failed(WishlistError)
    case _received([String])
  }
}

extension WishlistButtonView.ViewState {
  enum Reducer {
    static func reduce(
      _ state: inout WishlistButtonView.ViewState,
      with event: WishlistButtonView.Event
    ) {
      switch event {
      case let .setProductID(productID):
        state.update(productID: productID)
      case let ._received(wishlist):
        state.update(wishlist: wishlist)
      case ._failed:
        state.update()
      case ._addToWishlist:
        state.update(isWishlisted: true)
      case ._removeFromWishlist:
        state.update(isWishlisted: false)
      case .toggleWishlist: break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    /// local domain store
    let wishlistStore: WishlistStore
    let router: OGRoutePublishing
    init(
      wishlistStore: WishlistStore,
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()
    ) {
      self.wishlistStore = wishlistStore
      self.router = router
    }

    func callAsFunction(
      event: WishlistButtonView.Event,
      for state: WishlistButtonView.ViewState
    ) async
      -> WishlistButtonView.Event? {
      switch event {
      case let .toggleWishlist(isWishlist):
        guard let id = state.productID else { return nil }
        if isWishlist {
          return ._removeFromWishlist(id)
        } else {
          return ._addToWishlist(id)
        }
      case let ._addToWishlist(id):
        wishlistStore.dispatchDetached(WishlistAction.addToWishlist(id))
        return nil
      case let ._removeFromWishlist(id):
        wishlistStore.dispatchDetached(WishlistAction.removeFromWishlist(id))
        return nil
      case let ._failed(error):
        router.send(OGRoute(.errorBanner, data: ErrorBanner(title: error.localizedDescription)))
        DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
          router.dismiss(route: .errorBanner)
        }
        return nil
      case ._received, .setProductID:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private var cancellables = Set<AnyCancellable>()
    let wishlistStore: WishlistStore
    init(
      wishlistStore: WishlistStore
    ) {
      self.wishlistStore = wishlistStore
    }

    func configure(
      dispatch: @escaping (WishlistButtonView.Event) async -> Void
    ) async {
      await wishlistStore
        .statePublisher
        .map(\.wishlist)
        .removeDuplicates()
        .sink { wishlist in
          Task {
            await dispatch(._received(wishlist))
          }
        }
        .store(in: &cancellables)

      await wishlistStore
        .watchError(type: WishlistError.self)
        .sink { error in
          Task {
            switch error {
            case .addToWishlistFailed, .removeFromWishlistFailed:
              await dispatch(._failed(error))
            }
          }
        }
        .store(in: &cancellables)
    }
  }
}
