import OGCore
import OGRouter
import SwiftUI

// MARK: - ProductDetailDestinationProvider

public struct ProductDetailDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.productDetail
  }

  public func provide(_ route: OGRoute) -> some View {
    ProductDetailView(route: route)
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGRoute {
  public static let productDetail = OGRoute(OGIdentifier.productDetail.value)

  static func productDetail(data: ProductDetailDestinationProvider.RouteData?) -> OGRoute {
    OGRoute(OGRoute.productDetail, data: data)
  }
}

// MARK: - ProductDetailDestinationProvider.RouteData

extension ProductDetailDestinationProvider {
  struct RouteData: Codable {
    var productId: String
    var secondaryId: String?
  }
}
