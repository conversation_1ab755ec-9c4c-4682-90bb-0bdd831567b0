import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

typealias OGFlag = Skie.com_ottogroup_ogappkit_nativeui__api.Flag.__Sealed

extension Flag {
  var flag: OGFlag {
    onEnum(of: self)
  }
}

extension OGFlag {
  var name: String {
    switch self {
    case .mixMatch:
      ogL10n.ProductDetail.Gallery.Flag.MixMatch
    case .multiPack:
      ogL10n.ProductDetail.Gallery.Flag.Pack
    case .set:
      ogL10n.ProductDetail.Gallery.Flag.Set
    case .new:
      ogL10n.ProductDetail.Gallery.Flag.New
    case .sale:
      ogL10n.ProductDetail.Gallery.Flag.Sale
    case .exclusiveOnline:
      ogL10n.ProductDetail.Gallery.Flag.ExclusiveOnline
    case .priceHighlight:
      ogL10n.ProductDetail.Gallery.Flag.PriceHighlight
    case let .salesUnit(salesUnit):
      ogL10n.ProductDetail.Gallery.Flag.SalesUnit(quantity: String(salesUnit.salesUnitCount))
    }
  }

  var textColor: Color {
    switch self {
    case .mixMatch:
      OGColors.textOnPrimary.color
    case .multiPack:
      OGColors.textOnPrimary.color
    case .set:
      OGColors.textOnPrimary.color
    case .new:
      OGColors.textOnDark.color
    case .sale:
      OGColors.textOnDark.color
    case .exclusiveOnline:
      OGColors.textOnPrimary.color
    case .priceHighlight:
      OGColors.textOnPrimary.color
    case .salesUnit:
      OGColors.textOnPrimary.color
    }
  }

  var backgroundColor: Color {
    switch self {
    case .mixMatch:
      OGColors.primaryPrimary150.color
    case .multiPack:
      OGColors.primaryPrimary150.color
    case .set:
      OGColors.primaryPrimary150.color
    case .new:
      OGColors.primaryPrimary100.color
    case .sale:
      OGColors.accentSale.color
    case .exclusiveOnline:
      OGColors.primaryPrimary150.color
    case .priceHighlight:
      OGColors.primaryPrimary150.color
    case .salesUnit:
      OGColors.primaryPrimary150.color
    }
  }
}
