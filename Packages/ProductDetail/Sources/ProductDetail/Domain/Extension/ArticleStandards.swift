import OGAppKitSDK
import OGL10n
import UICatalog
import Foundation

extension ArticleStandards {
  var seals : [ArticleStandardsSeal] {
    (self as? ArticleStandardsStructuredSeals)?.seals ?? []
  }
  
  var sealsUrls : [URL] {
    ((self as? ArticleStandardsSealUrls)?.urls ?? []).compactMap({URL(string: $0)})
  }
  
  var informationLink : String? {
    (self as? ArticleStandardsSealUrls)?.informationLink
  }
}

extension ArticleStandardsSeal {

  var title: String {
    switch self {
    case .bioRe:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BioRe.Title
    case .gotsOrganic:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GotsOrganic.Title
    case .ocs100:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ocs100.Title
    case .ocsBlended:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OcsBlended.Title
    case .ivn:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ivn.Title
    case .oekoTex:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OekoTex.Title
    case .organicCotton:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OrganicCotton.Title
    case .gotsMadeWithOrganic:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GotsMadeWithOrganic.Title
    case .grs:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Grs.Title
    case .rcs100:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Rcs100.Title
    case .rcsBlended:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.RcsBlended.Title
    case .repreve:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Repreve.Title
    case .econyl:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Econyl.Title
    case .seaqual:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Seaqual.Title
    case .refibra:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Refibra.Title
    case .recycledMaterial:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.RecycledMaterial.Title
    case .responsibleDown:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.ResponsibleDown.Title
    case .responsibleWool:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.ResponsibleWool.Title
    case .downpass:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Downpass.Title
    case .goodCashmere:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GoodCashmere.Title
    case .ecovero:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ecovero.Title
    case .lyocell:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Lyocell.Title
    case .spinnova:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Spinnova.Title
    case .modal:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Modal.Title
    case .sustainableViscose:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.SustainableViscose.Title
    case .madeInGreen:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.MadeInGreen.Title
    case .bluesignProduct:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BluesignProduct.Title
    case .gruenerKnopf:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GruenerKnopf.Title
    case .leatherWorkingGroup:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.LeatherWorkingGroup.Title
    case .euecolabel:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Euecolabel.Title
    case .blauerEngel:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BlauerEngel.Title
    case .nordicSwan:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.NordicSwan.Title
    case .cottonMadeInAfrica:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.CottonMadeInAfrica.Title
    case .fairtradeCotton:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.FairtradeCotton.Title
    }
  }

  var detail: String {
    switch self {
    case .bioRe:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BioRe.Detail
    case .gotsOrganic:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GotsOrganic.Detail
    case .ocs100:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ocs100.Detail
    case .ocsBlended:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OcsBlended.Detail
    case .ivn:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ivn.Detail
    case .oekoTex:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OekoTex.Detail
    case .organicCotton:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.OrganicCotton.Detail
    case .gotsMadeWithOrganic:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GotsMadeWithOrganic.Detail
    case .grs:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Grs.Detail
    case .rcs100:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Rcs100.Detail
    case .rcsBlended:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.RcsBlended.Detail
    case .repreve:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Repreve.Detail
    case .econyl:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Econyl.Detail
    case .seaqual:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Seaqual.Detail
    case .refibra:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Refibra.Detail
    case .recycledMaterial:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.RecycledMaterial.Detail
    case .responsibleDown:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.ResponsibleDown.Detail
    case .responsibleWool:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.ResponsibleWool.Detail
    case .downpass:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Downpass.Detail
    case .goodCashmere:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GoodCashmere.Detail
    case .ecovero:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Ecovero.Detail
    case .lyocell:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Lyocell.Detail
    case .spinnova:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Spinnova.Detail
    case .modal:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Modal.Detail
    case .sustainableViscose:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.SustainableViscose.Detail
    case .madeInGreen:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.MadeInGreen.Detail
    case .bluesignProduct:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BluesignProduct.Detail
    case .gruenerKnopf:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.GruenerKnopf.Detail
    case .leatherWorkingGroup:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.LeatherWorkingGroup.Detail
    case .euecolabel:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.Euecolabel.Detail
    case .blauerEngel:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.BlauerEngel.Detail
    case .nordicSwan:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.NordicSwan.Detail
    case .cottonMadeInAfrica:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.CottonMadeInAfrica.Detail
    case .fairtradeCotton:
      ogL10n.ProductDetail.Information.ArticleStandards.Seals.FairtradeCotton.Detail
    }
  }

  var imageName: String? {
    switch self {
    case .bioRe:
      OGImages.pdpBioReSustainableTextilesStandard.name
    case .gotsOrganic:
      OGImages.pdpGlobalOrganicTextileStandardOrganic.name
    case .ocs100:
      OGImages.pdpOrganicContentStandardOcs100.name
    case .ocsBlended:
      OGImages.pdpOrganicContentStandardOcsBlended.name
    case .ivn:
      OGImages.pdpNaturtextilIvnZertifiziertBest.name
    case .oekoTex:
      OGImages.pdpOekoTexOrganicCotton.name
    case .organicCotton:
      OGImages.pdpBioBaumwolle.name
    case .gotsMadeWithOrganic:
      OGImages.pdpGlobalOrganicTextileStandardMadeWithOrganicMaterials.name
    case .grs:
      OGImages.pdpGrsGlobalStandard.name
    case .rcs100:
      OGImages.pdpRecycledClaimStandardRcs100.name
    case .rcsBlended:
      OGImages.pdpRecycledClaimStandardRcsBlended.name
    case .repreve:
      OGImages.pdpRepreveUnifiInc.name
    case .econyl:
      OGImages.pdpEconyl.name
    case .seaqual:
      OGImages.pdpSeaqual.name
    case .refibra:
      OGImages.pdpTencelMitRefibraTechnologie.name
    case .recycledMaterial:
      OGImages.pdpRecyceltesMaterial.name
    case .responsibleDown:
      OGImages.pdpReponsibleDownStandard.name
    case .responsibleWool:
      OGImages.pdpResponsibleWoolStandard.name
    case .downpass:
      OGImages.pdpDownpass.name
    case .goodCashmere:
      OGImages.pdpGoodCashmereStandard.name
    case .ecovero:
      OGImages.pdpLenzingEcovero.name
    case .lyocell:
      OGImages.pdpTencelLyocell.name
    case .spinnova:
      OGImages.pdpSpinnova.name
    case .modal:
      OGImages.pdpTencelModal.name
    case .sustainableViscose:
      OGImages.pdpNachhaltigeViskose.name
    case .madeInGreen:
      OGImages.pdpMadeInGreenByOkeoTex.name
    case .bluesignProduct:
      OGImages.pdpBluesignProduct.name
    case .gruenerKnopf:
      OGImages.pdpGruenerKnopf.name
    case .leatherWorkingGroup:
      OGImages.pdpLeatherWorkingGroup.name
    case .euecolabel:
      OGImages.pdpEuEcolabel.name
    case .blauerEngel:
      OGImages.pdpDerBlaueEngel.name
    case .nordicSwan:
      OGImages.pdpNordicSwanEcolabel.name
    case .cottonMadeInAfrica:
      OGImages.pdpCottonMadeInAfrica.name
    case .fairtradeCotton:
      OGImages.pdpFairtradeCotton.name
    }
  }
}

extension ArticleStandardsCategory {

  var imageName: String {
    switch self {
    case .animalWelfare:
      OGImages.icon24x24PdpAnimalWelfare.name
    case .improvedProduction:
      OGImages.icon24x24PdpImprovedProduction.name
    case .organicMaterials:
      OGImages.icon24x24PdpOrganicMaterials.name
    case .recycledMaterials:
      OGImages.icon24x24PdpRecycledMaterials.name
    case .responsiblySourcedMaterials:
      OGImages.icon24x24PdpRecycledMaterials.name
    case .supportingSocialInitiatives:
      OGImages.icon24x24PdpSupportSocialInitiatives.name
    }
  }

  var imageNameLarge: String {
    switch self {
    case .animalWelfare:
      OGImages.icon44x44PdpAnimalWelfare.name
    case .improvedProduction:
      OGImages.icon44x44PdpImprovedProduction.name
    case .organicMaterials:
      OGImages.icon44x44PdpOrganicMaterials.name
    case .recycledMaterials:
      OGImages.icon44x44PdpRecycledMaterials.name
    case .responsiblySourcedMaterials:
      OGImages.icon44x44PdpRecycledMaterials.name
    case .supportingSocialInitiatives:
      OGImages.icon44x44PdpSupportSocialInitiatives.name
    }
  }

  var title: String {
    switch self {
    case .animalWelfare:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.AnimalWelfare.Title
    case .improvedProduction:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.ImprovedProduction.Title
    case .organicMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.OrganicMaterials.Title
    case .recycledMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.RecycledMaterials.Title
    case .responsiblySourcedMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.ResponsiblySourcedMaterials.Title
    case .supportingSocialInitiatives:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.SupportingSocialInitiatives.Title
    }
  }

  var detail: String {
    switch self {
    case .animalWelfare:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.AnimalWelfare.Detail
    case .improvedProduction:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.ImprovedProduction.Detail
    case .organicMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.OrganicMaterials.Detail
    case .recycledMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.RecycledMaterials.Detail
    case .responsiblySourcedMaterials:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.ResponsiblySourcedMaterials.Detail
    case .supportingSocialInitiatives:
      ogL10n.ProductDetail.Information.ArticleStandards.Categories.SupportingSocialInitiatives.Detail
    }
  }
}
