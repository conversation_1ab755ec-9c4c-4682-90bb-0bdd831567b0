import OGAppKitSDK

// MARK: - LoadingComponent

protocol LoadingComponent {
  associatedtype ContentType: AnyObject
  var state: LoadingComponentState { get }
  static var empty: ContentType { get }
}

extension LoadingComponent {
  var isLoading: Bool {
    switch onEnum(of: state) {
    case .done:
      false
    case .loading:
      true
    }
  }

  var content: ContentType {
    switch state {
    case let loadingState as LoadingComponentStateLoading<ContentType>:
      loadingState.placeholderContent
    case let doneState as LoadingComponentStateDone<ContentType>:
      doneState.content
    default:
      Self.empty
    }
  }
}

// MARK: - AddToBasketButton + LoadingComponent

extension AddToBasketButton: LoadingComponent {
  typealias ContentType = AddToBasketButtonContent
  static var empty: AddToBasketButtonContent {
    AddToBasketButtonContentAddToBasket(productId: "")
  }

  var contentAddToBasket: AddToBasketButtonContentAddToBasket? {
    content as? AddToBasketButtonContentAddToBasket
  }

  var contentNotifyMe: AddToBasketButtonContentNotifyMe? {
    content as? AddToBasketButtonContentNotifyMe
  }
}

// MARK: - ProductAvailability + LoadingComponent

extension ProductAvailability: LoadingComponent {
  typealias ContentType = ProductAvailability.Content
  static var empty: ProductAvailability.Content {
    ProductAvailability.Content(
      availability: Availability(
        state: .unknown,
        quantity: 0,
        message: nil,
        deliveryTime: nil,
        notifyMeUrl: nil
      )
    )
  }
}

// MARK: - ProductColor + LoadingComponent

extension ProductColor: LoadingComponent {
  typealias ContentType = ProductColor.Content
  static var empty: ProductColor.Content {
    ProductColor.Content(colorName: "")
  }
}

// MARK: - ProductVariant + LoadingComponent

extension ProductVariant: LoadingComponent {
  typealias ContentType = ProductVariant.Content
  static var empty: ProductVariant.Content {
    ProductVariant
      .Content(
        dimensionName: "",
        variant: ProductDimensions.ProductDimensionVariantLink(
          name: "", productId: "",
          availability: Availability(state: .unknown, quantity: 0, message: nil, deliveryTime: nil, notifyMeUrl: nil),
          price: .init(currency: "", value: 0, oldValue: nil, isStartPrice: false),
          isSelected: false
        )
      )
  }
}

// MARK: - ProductColorDimension + LoadingComponent

extension ProductColorDimension: LoadingComponent {
  typealias ContentType = ProductColorDimension.Content
  static var empty: ProductColorDimension.Content {
    ProductColorDimension.Content(
      colorName: "",
      colors: []
    )
  }
}

// MARK: - ProductGallery + LoadingComponent

extension ProductGallery: LoadingComponent {
  typealias ContentType = ProductGallery.Content
  static var empty: ProductGallery.Content {
    ProductGallery.Content(
      images: [],
      flags: [],
      isWishlisted: false,
      productIdForWishlisting: ""
    )
  }
}

extension ProductGallery.Content {
  func hasEqualContent(to: ProductGallery.Content?) -> Bool {
    images == to?.images &&
      productIdForWishlisting == to?.productIdForWishlisting
  }
}

extension ProductRecommendationsRecommendedProduct {
  func hasEqualContent(to: ProductRecommendationsRecommendedProduct?) -> Bool {
    brandName == to?.brandName &&
      productId == to?.productId &&
      secondaryId == to?.secondaryId &&
      productIdForWishlisting == to?.productIdForWishlisting &&
      title == to?.title &&
      price == to?.price &&
      image == to?.image
  }
}

extension ProductRecommendations {
  func hasEqualContent(to: ProductRecommendations?) -> Bool {
    guard let to else { return false }
    switch (onEnum(of: state), onEnum(of: to.state)) {
    case let (.done(state), .done(toState)):
      return state.content.products.map { $0.hasEqualContent(to: $0) } == toState.content.products.map { $0.hasEqualContent(to: $0) }
    default:
      return false
    }
  }
}

extension GkAirRecommendations {
  func hasEqualContent(to: GkAirRecommendations?) -> Bool {
    guard let to else { return false }
    switch (onEnum(of: state), onEnum(of: to.state)) {
    case let (.done(state), .done(toState)):
      return state.content.products.map { $0.hasEqualContent(to: $0) } == toState.content.products.map { $0.hasEqualContent(to: $0) }
    default:
      return false
    }
  }
}

extension [GkAirRecommendations] {
  func hasEqualContent(to: [GkAirRecommendations]) -> Bool {
    zip(self, to).allSatisfy { $0.hasEqualContent(to: $1) }
  }
}

extension DynamicYieldRecommendations {
  func hasEqualContent(to: DynamicYieldRecommendations?) -> Bool {
    guard let to else { return false }
    switch (onEnum(of: state), onEnum(of: to.state)) {
    case let (.done(state), .done(toState)):
      return state.content.products.map { $0.hasEqualContent(to: $0) } == toState.content.products.map { $0.hasEqualContent(to: $0) }
    default:
      return false
    }
  }
}

extension [DynamicYieldRecommendations] {
  func hasEqualContent(to: [DynamicYieldRecommendations]) -> Bool {
    zip(self, to).allSatisfy { $0.hasEqualContent(to: $1) }
  }
}

// MARK: - ProductHeader + LoadingComponent

extension ProductHeader: LoadingComponent {
  typealias ContentType = ProductHeader.Content
  static var empty: ProductHeader.Content {
    ProductHeader.Content(
      title: "",
      brandName: "",
      sharingData: SharingData(
        url: ""
      ),
      isWishlisted: false,
      productIdForWishlisting: "",
      productId: ""
    )
  }
}

// MARK: - ProductInformation + LoadingComponent

extension ProductInformation: LoadingComponent {
  typealias ContentType = ProductInformation.Content
  static var empty: ProductInformation.Content {
    ProductInformation.Content(
      description: Description(
        articleNumber: "",
        bulletPoints: [],
        text: "",
        documents: []
      ),
      details: Details(
        attributesTable: Information.AttributesTable(
          sections: []
        )
      ),
      brand: nil,
      importantInformation: nil,
      articleStandards: nil
    )
  }
}

// MARK: - ProductPrice + LoadingComponent

extension ProductPrice: LoadingComponent {
  typealias ContentType = ProductPrice.Content
  static var empty: ProductPrice.Content {
    ProductPrice.Content(
      price: Price_(
        currency: "",
        value: 0,
        oldValue: nil,
        isStartPrice: false
      )
    )
  }
}

// MARK: - ProductRating + LoadingComponent

extension ProductRating: LoadingComponent {
  typealias ContentType = ProductRating.Content
  static var empty: ProductRating.Content {
    ProductRating.Content(
      rating: Rating(
        averageRating: 0,
        count: 0,
        ratingDistribution: [:]
      ),
      allReviewsUrl: ""
    )
  }
}

// MARK: - ProductTitle + LoadingComponent

extension ProductTitle: LoadingComponent {
  typealias ContentType = ProductTitle.Content
  static var empty: ProductTitle.Content {
    ProductTitle.Content(title: "")
  }
}

// MARK: - ProductReviews + LoadingComponent

extension ProductReviews: LoadingComponent {
  typealias ContentType = ProductReviews_Content
  static var empty: ProductReviews_Content {
    ProductReviews_ContentEmpty(writeReviewUrl: "")
  }
}

// MARK: - ProductDimensions + LoadingComponent

extension ProductDimensions: LoadingComponent {
  typealias ContentType = ProductDimensionsContent

  static var empty: ProductDimensionsContent {
    NestedDimensions(name: "", entries: [], isWishlisted: false, productIdForWishlisting: "", sizeAdvisorUrl: nil, sizeTableData: nil)
  }
}

// MARK: - AddedProduct + LoadingComponent

extension AddedProduct: LoadingComponent {
  typealias ContentType = AddedProduct.Content

  static var empty: AddedProduct.Content {
    AddedProduct.Content(productId: "", brandName: nil, title: "", selectedDimensionValues: [], price: Price_(currency: "", value: 0, oldValue: nil, isStartPrice: false), image: Image(url: "", thumbnailUrl: ""))
  }
}

// MARK: - ContinueShoppingButton + LoadingComponent

extension ContinueShoppingButton: LoadingComponent {
  typealias ContentType = ContinueShoppingButton.Content

  static var empty: ContinueShoppingButton.Content {
    ContinueShoppingButton.Content(continueShoppingUrl: nil)
  }
}

// MARK: - ShowBasketButton + LoadingComponent

extension ShowBasketButton: LoadingComponent {
  typealias ContentType = ShowBasketButton.Content

  static var empty: ShowBasketButton.Content {
    ShowBasketButton.Content(basketUrl: "")
  }
}

// MARK: - ShopUsps + LoadingComponent

extension ShopUsps: LoadingComponent {
  typealias ContentType = ShopUsps.Content

  static var empty: ShopUsps.Content {
    ShopUsps.Content(paybackPoints: 0)
  }
}

extension PromoBanner {
  var isLoading: Bool {
    switch onEnum(of: self) {
    case let .contentfulPromoBanner(contentfulPromoBanner):
      switch onEnum(of: contentfulPromoBanner.state) {
      case .done:
        false
      case .loading:
        true
      }
    case let .dynamicYieldPromoBanner(dynamicYieldPromoBanner):
      switch onEnum(of: dynamicYieldPromoBanner.state) {
      case .done:
        false
      case .loading:
        true
      }
    }
  }
    
  var content: PromoBannerContent {
    switch onEnum(of: self) {
    case let .contentfulPromoBanner(contentfulPromoBanner):
      switch contentfulPromoBanner.state {
      case let loadingState as LoadingComponentStateLoading<PromoBannerContent>:
        loadingState.placeholderContent
      case let doneState as LoadingComponentStateDone<PromoBannerContent>:
        doneState.content
      default:
        .empty
    }
    case let .dynamicYieldPromoBanner(dynamicYieldPromoBanner):
      switch dynamicYieldPromoBanner.state {
      case let loadingState as LoadingComponentStateLoading<PromoBannerContent>:
        loadingState.placeholderContent
      case let doneState as LoadingComponentStateDone<PromoBannerContent>:
        doneState.content
      default:
        .empty
      }
    }
  }
}

extension PromoBannerContent {
  static var empty: PromoBannerContent {
    PromoBannerContent(text: "", infoText: nil, promoCode: nil, trackingEvents: .init(view: View.ProductDetailPromotion(itemId: "", itemName: "", coupon: nil, creativeName: nil, creativeSlot: nil, promotionId: nil, promotionName: nil, additionalParameters: [:]), click: Interaction.ProductDetailSelectPromotion(itemId: "", itemName: "", coupon: nil, creativeName: nil, creativeSlot: nil, promotionId: nil, promotionName: nil, additionalParameters: [:])))
  }
}

