import Combine
import Foundation
import OGAppKitSDK
import OGCore
import OGDIService
import OGDomainStore
import OGRouter

typealias ProductDetailStore = OGDomainStore<ProductDetailState, ProductDetailAction>

extension OGDomainStoreFactory {
  static func make(route: OGRoute) -> ProductDetailStore {
    let service = ProductDetailContainer.shared.service()
    return ProductDetailStore(
      reducer: ProductDetailState.Reducer.reduce,
      middlewares: ProductDetailState.Middleware(service: service, url: route.url, routeData: route.getData()),
      connector: ProductDetailState.ProductDetailConnector(service: service)
    )
  }
}

// MARK: - StickyBasket

struct StickyBasket: Equatable {
  let price: ProductPrice?
  let basket: AddToBasketButton?
}

// MARK: - ProductDetailState

struct ProductDetailState: OGDomainState {
  private(set) var isAwaitingUpdate: Bool
  private(set) var components: [OGProductDetailComponent]
  private(set) var header: ProductHeader?
  private(set) var componentConfigsJson: String
  private(set) var screenId: String?
  private(set) var basket: StickyBasket?
  private(set) var componentConfigsDidChange: Bool
  init(
    isAwaitingUpdate: Bool = false,
    components: [OGProductDetailComponent] = [],
    componentConfigsJson: String = "",
    header: ProductHeader? = nil,
    basket: StickyBasket? = nil,
    screenId: String? = nil,
    componentConfigsDidChange: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.components = components
    self.componentConfigsJson = componentConfigsJson
    self.header = header
    self.basket = basket
    self.componentConfigsDidChange = componentConfigsDidChange
    self.screenId = screenId
  }

  static let initial: Self = .init()

  mutating func update(
    isAwaitingUpdate: Bool? = nil,
    components: [OGProductDetailComponent]? = nil,
    componentConfigsJson: String? = nil,
    header: ProductHeader? = nil,
    basket: StickyBasket? = nil,
    componentConfigsDidChange: Bool? = nil,
    screenId: String? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate ?? self.isAwaitingUpdate
    self.components = components ?? self.components
    self.componentConfigsJson = componentConfigsJson ?? self.componentConfigsJson
    self.header = header ?? self.header
    self.basket = basket ?? self.basket
    self.componentConfigsDidChange = componentConfigsDidChange ?? self.componentConfigsDidChange
    self.screenId = screenId ?? self.screenId
  }
}

// MARK: - ProductDetailAction

enum ProductDetailAction: OGDomainAction {
  case getDetailScreen
  case getDetailScreenWith(id: String, secondaryId: String?)
  case updateProductColorSelection(id: String)
  case updateProductVariantSelection(id: String)
  case updateBadgeCount
  /// private
  case _setComponentConfigsJson(String)
  case _setDetailScreen([OGProductDetailComponent])
  case _setScreenId(String)
  case _throwError(ProductDetailError)
}

extension ProductDetailState {
  enum Reducer {
    static func reduce(
      _ state: inout ProductDetailState,
      with action: ProductDetailAction
    ) {
      switch action {
      case .getDetailScreen:
        state.update(isAwaitingUpdate: true)

      case let ._setDetailScreen(components):
        state.update(
          isAwaitingUpdate: false,
          components: components.componentsWithoutHeader,
          header: components.productHeader,
          basket: StickyBasket(
            price: components.productPrice,
            basket: components.productBasket
          )
        )

      case let ._setComponentConfigsJson(componentConfigsJson):
        state.update(componentConfigsJson: componentConfigsJson, componentConfigsDidChange: componentConfigsJson.sorted() != state.componentConfigsJson.sorted())
      case let ._setScreenId(screenId):
        state.update(screenId: screenId)
      case ._throwError:
        state.update(
          isAwaitingUpdate: false
        )
      case .getDetailScreenWith, .updateBadgeCount, .updateProductColorSelection, .updateProductVariantSelection:
        break
      }
    }
  }

  // MARK: - ProductDetailDomainMiddleware

  struct Middleware: OGDomainMiddleware {
    private let service: ProductDetailServing
    private let logger: any OGLoggingDistributable

    private let url: URL
    private let routeData: ProductDetailDestinationProvider.RouteData?
    private let wishlistStore: WishlistStore
    private let badgeStore: BadgeStore

    init(
      service: ProductDetailServing,
      url: URL,
      routeData: ProductDetailDestinationProvider.RouteData? = nil,
      logger: any OGLoggingDistributable = OGCoreContainer.shared.logger(),
      wishlistStore: WishlistStore = ProductDetailContainer.shared.wishlistStore(),
      badgeStore: BadgeStore = OGDomainStoreFactory.make()
    ) {
      self.service = service
      self.logger = logger
      self.url = url
      self.routeData = routeData
      self.wishlistStore = wishlistStore
      self.badgeStore = badgeStore
    }

    func callAsFunction(
      action: ProductDetailAction,
      for state: ProductDetailState
    ) async throws
      -> ProductDetailAction? {
      switch action {
      case let .getDetailScreenWith(id, secondaryId):
        await service.fetchProductDetail(productId: id, secondaryId: secondaryId, componentConfigsJson: state.componentConfigsJson)
        return nil
      case .getDetailScreen:
        if let routeData, !routeData.productId.isEmpty {
          await service.fetchProductDetail(productId: routeData.productId, secondaryId: routeData.secondaryId, componentConfigsJson: state.componentConfigsJson)
        } else if url != .empty {
          await service.fetchProductDetailScreen(for: url, componentConfigsJson: state.componentConfigsJson)
        }
        return nil
      case ._setComponentConfigsJson:
        guard !state.componentConfigsJson.isEmpty, state.componentConfigsDidChange else { return nil }
        return .getDetailScreen
      case .updateBadgeCount:
        let wishlist = try await service.refreshWishlistCount()
        badgeStore.dispatchDetached(BadgeStoreAction.setWishlistCount(wishlist.count))
        wishlistStore.dispatchDetached(WishlistAction._receivedWishlist(wishlist))

        let basketCount = try await service.refreshBasketCount()
        badgeStore.dispatchDetached(BadgeStoreAction.setBasketCount(basketCount))

        return nil
      case let .updateProductColorSelection(id):
        guard let screenId = state.screenId else {
          return .getDetailScreenWith(id: id, secondaryId: nil)
        }
        await service.updateProductColorSelection(productId: id, screenId: screenId)
        return nil
      case let .updateProductVariantSelection(id):
        guard let screenId = state.screenId else {
          return .getDetailScreenWith(id: id, secondaryId: nil)
        }
        await service.updateProductVariantSelection(productId: id, screenId: screenId)
        return nil
      case let ._throwError(error):
        logger.log(.critical, domain: .service, message: error.localizedDescription)
        throw error
      case ._setDetailScreen, ._setScreenId:
        return nil
      }
    }
  }

  actor ProductDetailConnector: OGDomainConnector {
    private let service: ProductDetailServing
    private let feature: ProductDetailFeatureAdaptable
    private var cancellables = Set<AnyCancellable>()
    private var successTask: Task<Void, Never>?
    private var errorTask: Task<Void, Never>?
    init(
      service: ProductDetailServing,
      feature: ProductDetailFeatureAdaptable = ProductDetailFeatureAdapterContainer.shared.productDetail()
    ) {
      self.service = service
      self.feature = feature
    }

    public func configure(
      dispatch: @escaping (ProductDetailAction) async -> Void
    ) async {
      feature.configuration
        .map(\.components)
        .removeDuplicates()
        .sink { components in
          Task {
            await dispatch(._setComponentConfigsJson(components))
          }
        }.store(in: &cancellables)

      let service = service
      successTask = Task.detached { [service] in
        for await detailScreen in await service.successStream {
          guard !Task.isCancelled else { break }
          await dispatch(._setDetailScreen(detailScreen.components))
          await dispatch(._setScreenId(detailScreen.screenId))
        }
      }

      errorTask = Task.detached { [service] in
        for await error in await service.errorStream {
          guard !Task.isCancelled else { break }
          await dispatch(._throwError(.unknown(error)))
        }
      }
    }

    deinit {
      successTask?.cancel()
      errorTask?.cancel()
    }
  }
}
