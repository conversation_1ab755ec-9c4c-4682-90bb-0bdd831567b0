import OGAirshipKitTestsUtils
import OGCoreTestsUtils
import OGDomainStore
import XCTest

@testable import Inbox

final class InboxDetailViewStoreConnectorTests: XCTestCase {
  func test_WHEN_messagesReceived_THEN_updatedMessagesDispatched() async {
    await withMainSerialExecutor {
      let exp = expectation(description: "Updated messages event dispatched")

      let sut = InboxDetailView.Connector(
        message: .stubNotRead,
        inboxStore: InboxStore.makeMock(
          initialState: .init(
            messages: .stub
          )
        )
      )

      var actualEvents: [InboxDetailView.Event] = []
      await sut.configure { event in
        actualEvents.append(event)
        exp.fulfill()
      }

      await fulfillment(of: [exp])

      XCTAssertEqual(
        [._updated(message: .stubNotRead)],
        actualEvents
      )
    }
  }
}
