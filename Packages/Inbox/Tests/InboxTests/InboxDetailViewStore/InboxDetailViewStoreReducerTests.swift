import OGAirshipKit
import OGAirshipKitTestsUtils
import XCTest

@testable import Inbox

final class InboxDetailViewStoreReducerTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_updated_THEN_stateUpdated() throws {
    var expectedState = InboxDetailView.ViewState()
    expectedState.update(url: InboxMessage.stubNotRead.messageBodyURL)
    var state = InboxDetailView.ViewState.initial
    InboxDetailView.Reducer.reduce(
      &state,
      with: ._updated(message: .stubNotRead)
    )
    XCTAssertEqual(
      expectedState,
      state
    )
  }

  func test_GIVEN_initialState_WHEN_authHeader_THEN_stateUpdated() throws {
    var expectedState = InboxDetailView.ViewState()
    expectedState.update(authHeader: .stub)
    var state = InboxDetailView.ViewState.initial
    InboxDetailView.Reducer.reduce(
      &state,
      with: ._authHeader(.stub)
    )
    XCTAssertEqual(
      expectedState,
      state
    )
  }

  func test_GIVEN_initialState_WHEN_updated_AND_authHeader_THEN_requestUpdated() throws {
    var expectedRequest = URLRequest(url: InboxMessage.stubNotRead.messageBodyURL!)
    expectedRequest.setValue(.stub, forHTTPHeaderField: "Authorization")

    var state = InboxDetailView.ViewState.initial
    InboxDetailView.Reducer.reduce(
      &state,
      with: ._updated(message: .stubNotRead)
    )

    XCTAssertNotEqual(
      expectedRequest,
      state.request
    )

    InboxDetailView.Reducer.reduce(
      &state,
      with: ._authHeader(.stub)
    )

    XCTAssertEqual(
      expectedRequest,
      state.request
    )
  }

  func test_GIVEN_initialState_WHEN_didAppear_THEN_stateNotUpdated() throws {
    var state = InboxDetailView.ViewState.initial
    InboxDetailView.Reducer.reduce(
      &state,
      with: .didAppear
    )
    XCTAssertEqual(
      InboxDetailView.ViewState.initial,
      state
    )
  }

  func test_GIVEN_initialState_WHEN_trackDidAppear_THEN_stateNotUpdated() throws {
    var state = InboxDetailView.ViewState.initial
    InboxDetailView.Reducer.reduce(
      &state,
      with: ._trackDidAppear
    )
    XCTAssertEqual(
      InboxDetailView.ViewState.initial,
      state
    )
  }
}
