import OGAirshipKit
import <PERSON><PERSON>irship<PERSON>itTestsUtils
import OGN<PERSON><PERSON>
import <PERSON><PERSON>outerTestsUtils
import OGTracker
import OGTrackerTestsUtils
import XCTest

@testable import Inbox

final class InboxRowViewStoreMiddlewareTests: XCTestCase {
  func test_GIVEN_initialState_WHEN_updated_THEN_noNextEvent() async {
    let sut = InboxRow.Middleware(messageId: .stub)
    let nextEvent = await sut.callAsFunction(
      event: ._updated(message: .stubNotRead),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_GIVEN_initialState_WHEN_update_THEN_noNextEvent() async {
    let sut = InboxRow.Middleware(messageId: .stub)
    let nextEvent = await sut.callAsFunction(
      event: ._update(shouldShowThumbnails: true),
      for: .initial
    )
    XCTAssertNil(nextEvent)
  }

  func test_WHEN_didTap_THEN_markAsReadDispatchedToInboxStore_sendRoute_AND_trackDidTap() async {
    let inboxStoreMock = InboxStore.mock
    let routerMock = OGRoutePublisherMock()
    let sut = InboxRow.Middleware(
      inboxStore: inboxStoreMock,
      messageId: .stub,
      router: routerMock
    )
    let state = InboxRow.ViewState(message: .stubNotRead)
    let nextEvent = await sut.callAsFunction(
      event: .didTap,
      for: state
    )
    let route = OGRoute(OGIdentifier.inboxDetail.value, data: InboxMessage.stubNotRead)

    XCTAssertEqual(nextEvent, ._trackDidTap)
    XCTAssertEqual(1, routerMock.mock.sendCalls.callsCount)
    XCTAssertEqual(route.url.absoluteString, routerMock.mock.sendCalls.latestCall?.url.absoluteString)

    if let routeData = route.data, let latestCallData = routerMock.mock.sendCalls.latestCall?.data {
      let routeMessage = try? JSONDecoder().decode(InboxMessage.self, from: routeData)
      let latestCallMessage = try? JSONDecoder().decode(InboxMessage.self, from: latestCallData)
      XCTAssertEqual(routeMessage, latestCallMessage)
    }

    XCTAssertEqual(1, inboxStoreMock.dispatchDetachedCallCount)
    XCTAssertEqual(
      InboxAction.markAsRead(messageId: .stub),
      inboxStoreMock.dispatchDetachedArg
    )
  }

  func test_WHEN_trackDidTap_THEN_noNextEvent() async {
    let sut = InboxRow.Middleware(messageId: .stub)
    let state = InboxRow.ViewState(message: .stubNotRead)
    let nextEvent = await sut.callAsFunction(
      event: ._trackDidTap,
      for: state
    )

    XCTAssertNil(nextEvent)
  }

  func test_WHEN_trackDidTap_THEN_OpenInboxMessage_eventTracked() async {
    let tracker = OGTrackerMock()
    let sut = InboxRow.Middleware(
      messageId: .stub,
      tracker: tracker
    )
    let state = InboxRow.ViewState(message: .stubNotRead)
    let _ = await sut.callAsFunction(
      event: ._trackDidTap,
      for: state
    )

    let expectedEvent = tracker.mock.multiplatformTrackCalls.latestCall as? InteractionEvent.OpenInboxMessage
    let actualEvent = InteractionEvent.OpenInboxMessage()

    XCTAssertEqual(expectedEvent, actualEvent)
    XCTAssertEqual(tracker.mock.multiplatformTrackCalls.callsCount, 1)
  }
}
