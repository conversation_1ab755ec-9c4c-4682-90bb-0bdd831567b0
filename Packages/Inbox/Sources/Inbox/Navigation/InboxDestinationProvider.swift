import OGAirshipKit
import OGDIService
import OGIdentifier
import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - InboxDestinationProvider

public struct InboxDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router
  public let identifier: OGIdentifier = .inbox

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle { title in
      InboxView(title: title)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.Inbox.Title
  }
}

// MARK: - InboxDetailDestinationProvider

public struct InboxDetailDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGAirshipContainer.inboxCoordinator) var inboxCoordinator

  public let identifier: OGIdentifier = .inboxDetail

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    if let message: InboxMessage = route.getData(),
       let navigationDelegate = inboxCoordinator?.createNavigationDelegate() {
      viewWithTitle(for: route) { title in
        InboxDetailView(title: title, message: message, navigationDelegate: navigationDelegate)
      }
    }
  }

  public func title(for route: OGRoute?) -> String? {
    guard let message: InboxMessage = route?.getData() else { return nil }
    return message.title
  }
}
