import AppCore
import Combine
import Foundation
import HTTPTypes
import OGAirshipKit
import OGCore
import OGDIService
import OGDomainStore
import OGRouter
import OGTracker
import OGViewStore

// MARK: - InboxDetailView.Store

extension InboxDetailView {
  typealias Store = OGViewStore<ViewState, Event>
}

extension InboxDetailView {
  @MainActor
  static func make(message: InboxMessage) -> Store {
    InboxDetailView.Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector(message: message)
    )
  }
}

// MARK: - InboxDetailView.Event

extension InboxDetailView {
  enum Event: OGViewEvent {
    case didAppear
    /// Private events
    case _updated(message: InboxMessage)
    case _authHeader(String?)
    case _trackDidAppear
  }
}

// MARK: - InboxDetailView.ViewState

extension InboxDetailView {
  struct ViewState: OGViewState {
    private(set) var authHeader: String?
    private(set) var url: URL = .empty
    private(set) var request: URLRequest = .empty
    static var initial: InboxDetailView.ViewState = .init()

    mutating func update(
      url: URL? = nil,
      authHeader: String? = nil
    ) {
      self.authHeader = authHeader ?? self.authHeader
      self.url = url ?? self.url
      if let authHeader {
        request = URLRequest(url: self.url)
        request.setValue(authHeader, forHTTPHeaderField: HTTPField.Name.authorization.rawName)
      }
    }
  }
}

extension InboxDetailView {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      // Private events
      case let ._updated(message):
        guard let messageBodyURL = message.messageBodyURL else { return }
        state.update(url: messageBodyURL)

      case let ._authHeader(authHeader):
        state.update(authHeader: authHeader)

      case ._trackDidAppear, .didAppear:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let inboxCoordinator: OGAirshipMessageCenterCoordinable?
    private let tracker: OGTrackerProtocol

    init(
      inboxCoordinator: OGAirshipMessageCenterCoordinable? = OGAirshipContainer.shared.inboxCoordinator(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.inboxCoordinator = inboxCoordinator
      self.tracker = tracker
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case .didAppear:
        return ._trackDidAppear

      // Private events
      case ._updated:
        let authHeader = await inboxCoordinator?.authHeader()
        return ._authHeader(authHeader)

      case ._authHeader:
        return nil

      case ._trackDidAppear:
        tracker.multiplatformTrack(event: ViewEvent.ScreenInAppInboxMessage())
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let inboxStore: any OGDomainStoreViewStoreConsumable<InboxState>
    private let message: InboxMessage
    private var cancellables = Set<AnyCancellable>()
    init(
      message: InboxMessage,
      inboxStore: any OGDomainStoreViewStoreConsumable<InboxState> = InboxContainer.shared.inboxStore()
    ) {
      self.inboxStore = inboxStore
      self.message = message
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await inboxStore
        .watch(keyPath: \.messages)
        .compactMap { messages in
          messages.first { message in
            message == self.message
          }
        }
        .sink { message in
          Task {
            await dispatch(._updated(message: message))
          }
        }
        .store(in: &cancellables)
    }
  }
}
