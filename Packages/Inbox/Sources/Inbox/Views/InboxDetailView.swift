import OGAirshipKit
import OGDIService
import OGNavigationBar
import <PERSON>GRouter
import OGWebView
import Swift
import SwiftUI
import WebKit

// MARK: - InboxDetailView

struct InboxDetailView: View {
  @StateObject private var webViewStore: OGWebViewStore
  @State private var showProgressView = true
  @StateObject private var viewStore: Store

  private let title: String

  public init(title: String, message: InboxMessage, navigationDelegate: WKNavigationDelegate) {
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.make(message: message))
    _webViewStore = StateObject(
      wrappedValue: OGWebViewStore(
        route: .empty,
        navigationDelegate: navigationDelegate
      )
    )
  }

  var body: some View {
    OGNavigationBar(titleView: Text(title)) {
      content
        .onAppear {
          Task {
            await viewStore.dispatch(.didAppear)
          }
        }
    }
    .onChange(of: viewStore.request, perform: { value in
      webViewStore.update(request: value)
    })
  }

  @ViewBuilder private var content: some View {
    progressView
    OGWebView(
      with: webViewStore.webView
    )
  }

  @ViewBuilder private var progressView: some View {
    OGWebViewProgressView(estimatedProgress: $webViewStore.estimatedProgress)
  }
}
