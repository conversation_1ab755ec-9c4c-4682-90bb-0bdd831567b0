import OGNavigationBar
import PushPromotion
import SwiftUI

// MARK: - InboxView

public struct InboxView: View {
  @StateObject private var viewStore: Store

  private let title: String

  public init(title: String) {
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    OGNavigationBar(titleView: Text(title)) {
      ZStack {
        content()
          .onAppear {
            Task {
              await viewStore.dispatch(.didAppear)
            }
          }
        VStack {
          Spacer()
          PushPromotionBannerView()
        }
      }
    }
  }

  @ViewBuilder
  private func content() -> some View {
    if viewStore.messagesAvailable {
      InboxListView()
    } else {
      EmptyInboxView()
    }
  }
}

#Preview {
  InboxView(title: "Title")
}
