import AppCore
import Combine
import OGAirshipKit
import OGCore
import OGDIService
import OGDomainStore
import OGL10n
import OGRouter
import OGTracker
import OGViewStore

// MARK: - InboxRow.Store

extension InboxRow {
  typealias Store = OGViewStore<ViewState, Event>
}

extension InboxRow {
  @MainActor
  static func make(id: String) -> Store {
    InboxRow.Store(
      reducer: Reducer.reduce,
      middleware: Middleware(messageId: id),
      connector: Connector(messageId: id)
    )
  }
}

// MARK: - InboxRow.Event

extension InboxRow {
  enum Event: OGViewEvent, Hashable {
    case didTap

    /// Private events
    case _updated(message: InboxMessage)
    case _update(shouldShowThumbnails: Bool)
    case _trackDidTap
  }
}

// MARK: - InboxRow.ViewState

extension InboxRow {
  struct ViewState: OGViewState {
    fileprivate(set) var message: InboxMessage?
    private(set) var shouldShowThumbnails: Bool = false

    var messageAccessibilityLabelText: String {
      guard let message else { return "" }
      let readStatus: String = message.wasRead ? ogL10n.Inbox.Overview.Message.Read.ContentDescription : ogL10n.Inbox.Overview.Message.Unread.ContentDescription

      let title = message.title
      let summary = message.accessibilityTruncatedSummary

      let formattedDate = message.accessibilityFormattedDate

      let titleAndSummary = summary.isEmpty ? title : "\(title) – \(summary)"
      return "\(readStatus) – \(titleAndSummary). \(formattedDate)"
    }

    mutating func update(
      shouldShowThumbnails: Bool
    ) {
      self.shouldShowThumbnails = shouldShowThumbnails
    }

    static var initial: InboxRow.ViewState = .init()
  }
}

extension InboxRow {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case ._trackDidTap, .didTap:
        break

      // Private events
      case let ._update(shouldShowThumbnails):
        state.update(shouldShowThumbnails: shouldShowThumbnails)

      case let ._updated(message):
        state.message = message
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let inboxStore: OGDomainActionDetachedDispatchable
    private let messageId: String
    private let router: OGRoutePublishing
    private let tracker: OGTrackerProtocol

    init(
      inboxStore: OGDomainActionDetachedDispatchable = InboxContainer.shared.inboxStore(),
      messageId: String,
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      tracker: OGTrackerProtocol = OGTrackerContainer.shared.tracker()
    ) {
      self.inboxStore = inboxStore
      self.messageId = messageId
      self.router = router
      self.tracker = tracker
    }

    func callAsFunction(
      event: Event,
      for state: ViewState
    ) async -> Event? {
      switch event {
      case .didTap:
        guard
          let message = state.message
        else { return nil }
        let id = message.id.value
        inboxStore.dispatchDetached(InboxAction.markAsRead(messageId: id))
        let route = OGRoute(OGIdentifier.inboxDetail.value, data: message)
        router.send(route)
        return ._trackDidTap

      // Private events
      case ._update, ._updated:
        return nil

      case ._trackDidTap:
        tracker.multiplatformTrack(event: InteractionEvent.OpenInboxMessage())
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let inboxStore: any OGDomainStoreViewStoreConsumable<InboxState>
    private let inboxFeature: InboxFeatureAdaptable
    private let messageId: String
    private var cancellables = Set<AnyCancellable>()

    init(
      inboxStore: any OGDomainStoreViewStoreConsumable<InboxState> = InboxContainer.shared.inboxStore(),
      inboxFeature: InboxFeatureAdaptable = InboxFeatureAdapterContainer.shared.inbox(),
      messageId: String
    ) {
      self.inboxStore = inboxStore
      self.inboxFeature = inboxFeature
      self.messageId = messageId
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      inboxFeature
        .configuration
        .sink { value in
          Task {
            await dispatch(._update(shouldShowThumbnails: value.shouldShowThumbnails))
          }
        }
        .store(in: &cancellables)

      await inboxStore
        .watch(keyPath: \.messages)
        .compactMap { messages in
          messages.first { message in
            message.id.value == self.messageId
          }
        }
        .sink { message in
          Task {
            await dispatch(._updated(message: message))
          }
        }
        .store(in: &cancellables)
    }
  }
}
