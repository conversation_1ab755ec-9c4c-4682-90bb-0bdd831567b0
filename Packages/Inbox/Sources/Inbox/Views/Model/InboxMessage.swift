import AppCore
import Foundation
import OGAirshipKit
import OGIdentifier

// MARK: - InboxMessage

public struct InboxMessage: Equatable, Hashable, Identifiable, Sendable {
  public let id: Identifier<Self>
  public let dateAsText: String
  public let messageBodyURL: URL?
  public let summary: String?
  public let title: String
  public let wasRead: Bool
  public let listIcon: String?
  public let date: Date

  public init(
    id: Identifier<Self>,
    dateAsText: String,
    messageBodyURL: URL?,
    summary: String?,
    title: String,
    wasRead: Bool,
    listIcon: String? = nil,
    date: Date
  ) {
    self.id = id
    self.dateAsText = dateAsText
    self.messageBodyURL = messageBodyURL
    self.summary = summary
    self.title = title
    self.wasRead = wasRead
    self.listIcon = listIcon
    self.date = date
  }
}

extension OGAirshipInboxMessage {
  public func toInboxMessage(locale: Locale? = nil) -> InboxMessage {
    InboxMessage(
      id: Identifier(value: id.value),
      dateAsText: date.formattedLocalizedYesterday(locale: locale),
      messageBodyURL: messageBodyURL,
      summary: summary,
      title: title,
      wasRead: wasRead,
      listIcon: listIcon,
      date: date
    )
  }
}

extension InboxMessage {
  var accessibilityFormattedDate: String {
    let currentLocale = Locale.current

    let accessibleTimeFormatter = DateFormatter()
    accessibleTimeFormatter.locale = currentLocale
    accessibleTimeFormatter.dateStyle = .none
    accessibleTimeFormatter.timeStyle = .short
    accessibleTimeFormatter.formattingContext = .standalone

    return accessibleTimeFormatter.string(from: date)
  }

  private func createDateFormatter(format: String) -> DateFormatter {
    let formatter = DateFormatter()
    formatter.dateFormat = format
    return formatter
  }

  var accessibilityTruncatedSummary: String {
    guard let summary else {
      return ""
    }

    let maxChars = 40

    if summary.count > maxChars {
      let truncatedText = String(summary.prefix(maxChars))
      return "\(truncatedText)..."
    }

    return summary
  }
}

// MARK: - InboxMessage + Codable

extension InboxMessage: Codable {
  enum CodingKeys: String, CodingKey {
    case id
    case dateAsText
    case messageBodyURL
    case summary
    case title
    case wasRead
    case listIcon
    case date
  }

  public init(from decoder: Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)

    let idValue = try container.decode(String.self, forKey: .id)
    self.id = Identifier(value: idValue)

    self.dateAsText = try container.decode(String.self, forKey: .dateAsText)
    self.messageBodyURL = try container.decodeIfPresent(URL.self, forKey: .messageBodyURL)
    self.summary = try container.decodeIfPresent(String.self, forKey: .summary)
    self.title = try container.decode(String.self, forKey: .title)
    self.wasRead = try container.decode(Bool.self, forKey: .wasRead)
    self.listIcon = try container.decodeIfPresent(String.self, forKey: .listIcon)
    self.date = try container.decode(Date.self, forKey: .date)
  }

  public func encode(to encoder: Encoder) throws {
    var container = encoder.container(keyedBy: CodingKeys.self)

    try container.encode(id.value, forKey: .id)
    try container.encode(dateAsText, forKey: .dateAsText)
    try container.encodeIfPresent(messageBodyURL, forKey: .messageBodyURL)
    try container.encodeIfPresent(summary, forKey: .summary)
    try container.encode(title, forKey: .title)
    try container.encode(wasRead, forKey: .wasRead)
    try container.encodeIfPresent(listIcon, forKey: .listIcon)
    try container.encode(date, forKey: .date)
  }
}
