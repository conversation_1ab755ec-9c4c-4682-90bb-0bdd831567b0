import OGAsyncImage
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ScratchView

struct ScratchView: View {
  @StateObject private var viewStore: Self.Store

  private let gridCellSize: CGFloat = 30.0 // Size of each grid cell (adjustable for precision)
  private let strokeWidth: CGFloat = 40.0 // Stroke width for drawing

  @SwiftUI.State private var imageSize: CGSize = .zero
  @SwiftUI.State private var scratchedCells: Set<CGPoint> = []
  @SwiftUI.State private var lastDrawingPoint: CGPoint?
  @SwiftUI.State private var currentDrawingPath = Path()

  init(game: GameScratchADeal) {
    _viewStore = StateObject(wrappedValue: Self.make(game: game))
  }

  var body: some View {
    ZStack {
      imageView
        .accessibilityHidden(true)

      scratchMask
        .gesture(scratchGesture)
        .accessibilityAction {
          Task {
            await viewStore.dispatch(.didScratch(scratchedPercentage: 100))
          }
        }
        .accessibilityLabel(viewStore.accessibilityText)
        .accessibilityAddTraits(.isButton)
    }
  }

  private var imageView: some View {
    GeometryReader { proxy in
      OGAsyncImage(
        url: viewStore.gameImageUrl,
        frame: .init(isEnabled: true, alignment: .center),
        fallbackView: {
          ResizableScaleToFill(
            image: OGImages.dealsOnboardingScratch.image
          )
        }
      )
      .onAppear {
        imageSize = proxy.size
      }
    }
  }

  private var scratchMask: some View {
    Rectangle()
      .mask {
        Canvas { context, _ in
          context.fill(currentDrawingPath, with: .color(.black))
          context.blendMode = .destinationOut
        }
      }
      .blendMode(.destinationOut)
  }

  private var scratchGesture: some Gesture {
    DragGesture(minimumDistance: 0, coordinateSpace: .local)
      .onChanged(handleDragChanged)
      .onEnded { _ in
        lastDrawingPoint = nil
      }
      .simultaneously(with: TapGesture().onEnded(handleTapEnded))
  }

  private func handleDragChanged(_ value: DragGesture.Value) {
    let currentPoint = value.location

    if let last = lastDrawingPoint {
      currentDrawingPath.addPath(
        createLine(
          from: last,
          to: currentPoint,
          width: strokeWidth
        )
      )
    } else {
      currentDrawingPath.addEllipse(
        in: CGRect(
          x: currentPoint.x - strokeWidth / 2,
          y: currentPoint.y - strokeWidth / 2,
          width: strokeWidth,
          height: strokeWidth
        )
      )
    }

    lastDrawingPoint = currentPoint

    let gridPoint = CGPoint(x: Int(currentPoint.x / gridCellSize), y: Int(currentPoint.y / gridCellSize))
    scratchedCells.insert(gridPoint)

    Task {
      guard !viewStore.didEndScratching else { return }
      await viewStore.dispatch(.didScratch(scratchedPercentage: getScratchPercentage()))
    }
  }

  private func handleTapEnded() {
    if let location = scratchedCells.first {
      scratchedCells.insert(location)
    }
  }

  private func getScratchPercentage() -> Double {
    let totalCells = Int((imageSize.width / gridCellSize) * (imageSize.height / gridCellSize))
    let scratchedCount = scratchedCells.count
    return Double(scratchedCount) / Double(totalCells)
  }

  private func createLine(from start: CGPoint, to end: CGPoint, width: CGFloat) -> Path {
    var newPath = Path()
    newPath.move(to: start)
    newPath.addLine(to: end)
    return newPath.strokedPath(.init(lineWidth: width, lineCap: .round, lineJoin: .round))
  }
}
