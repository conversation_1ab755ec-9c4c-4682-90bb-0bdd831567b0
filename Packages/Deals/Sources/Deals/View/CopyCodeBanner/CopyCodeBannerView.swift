import SwiftUI
import UICatalog

// MARK: - CopyCodeBannerView

struct CopyCodeBannerView: View {
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var isCodeCopied: Bool = false

  init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  var body: some View {
    if viewStore.code.isEmpty {
      Color.clear
        .frame(height: 0)
    } else {
      bannerContent
    }
  }

  private var bannerContent: some View {
    HStack(spacing: .zero) {
      contentStack
      Spacer()
      iconView
        .accessibilityHidden(true)
    }
    .background(
      ZStack {
        isCodeCopied ? OGColors.accentSuccess.color : OGColors.accentSale.color
      }
    )
    .foregroundStyle(OGColors.textOnDark.color)
    .transition(.opacity)
    .animation(.easeInOut(duration: 0.5), value: isCodeCopied)
    .accessibilityElement(children: .combine)
    .accessibilityLabel(isCodeCopied ? viewStore.codeCopiedMessage : viewStore.accessibilityLabel)
    .accessibilityAddTraits(.isButton)
    .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
  }

  private var contentStack: some View {
    VStack(alignment: .leading, spacing: Constants.padding) {
      titleView
        .accessibilityHidden(true)

      PromoCodeView(
        code: viewStore.code,
        style: .dashedBorder,
        isCodeCopied: $isCodeCopied
      ) {
        Task {
          await viewStore.dispatch(.copyCodeButtonTapped)
        }
      }
    }
    .padding(Constants.padding)
  }

  private var titleView: some View {
    Text(viewStore.title)
      .lineLimit(2)
      .font(for: .titleM)
      .multilineTextAlignment(.leading)
  }

  private var iconView: some View {
    OGImages.icon32x32Voucher.image
      .resizable()
      .aspectRatio(contentMode: .fit)
      .frame(
        width: Constants.voucherIconSize,
        height: Constants.voucherIconSize
      )
      .padding(Constants.padding)
  }
}

// MARK: - Constants

private enum Constants {
  static let voucherIconSize: CGFloat = 48.0
  static let padding: CGFloat = 12.0
}
