import AppCore
import Combine
import Foundation
import OGDomainStore
import OGL10n
import OGRouter
import OGViewStore
import UIKit

// MARK: - CopyCodeBannerView.Store

extension CopyCodeBannerView {
  typealias Store = OGViewStore<CopyCodeBannerView.State, CopyCodeBannerView.Event>
}

extension CopyCodeBannerView {
  @MainActor
  static func make() -> Store {
    CopyCodeBannerView.Store(
      reducer: CopyCodeBannerView.Reducer.reduce,
      middleware: CopyCodeBannerView.Middleware(),
      connector: CopyCodeBannerView.Connector()
    )
  }
}

// MARK: - CopyCodeBannerView.Event

extension CopyCodeBannerView {
  enum Event: OGViewEvent {
    case copyCodeButtonTapped
    /// Private
    case _setCode(String)
    case _setCampaigns([Campaign])
  }
}

// MARK: - CopyCodeBannerView.State

extension CopyCodeBannerView {
  struct State: OGViewState {
    private(set) var code: String = ""

    var title: String {
      ogL10n.Deals.DealVariant.Code.Banner.Title
    }

    var accessibilityLabel: String {
      [
        title,
        ogL10n.Deals.DealVariant.Code.CopyButton.Accessibility(code: code)
      ].joined(separator: ", ")
    }

    var codeCopiedMessage: String {
      ogL10n.General.Banner.Code.DidCopy
    }

    mutating func update(
      code: String? = nil
    ) {
      self.code = code ?? self.code
    }

    static var initial: CopyCodeBannerView.State = .init()
  }
}

// MARK: - CopyCodeBannerView.Reducer

extension CopyCodeBannerView {
  enum Reducer {
    static func reduce(state: inout State, with event: Event) {
      switch event {
      case ._setCampaigns, .copyCodeButtonTapped:
        break

      case let ._setCode(code):
        state.update(code: code)
      }
    }
  }
}

// MARK: - CopyCodeBannerView.Middleware

extension CopyCodeBannerView {
  final class Middleware: OGViewStoreMiddleware {
    private let storage: CampaignsProgressStoring
    private let tracker: CampaignTracking

    init(
      storage: CampaignsProgressStoring = DealsContainer.shared.campaignProgressStorage(),
      tracker: CampaignTracking = DealsContainer.shared.campaignTracker()
    ) {
      self.storage = storage
      self.tracker = tracker
    }

    func callAsFunction(event: Event, for state: State) async -> Event? {
      switch event {
      case .copyCodeButtonTapped:
        await tracker.trackCodeCopied(for: .banner)
        return nil

      case let ._setCampaigns(campaigns):
        if let deal = try? storage.getLatestSavedDealProgress(for: campaigns).deal as? DealCode {
          return ._setCode(deal.code)
        }
        return nil

      case ._setCode:
        return nil
      }
    }
  }
}

// MARK: - CopyCodeBannerView.Connector

extension CopyCodeBannerView {
  actor Connector: OGViewStoreConnector {
    private let campaignsListStore: any OGDomainStoreViewStoreConsumable<CampaignsState>
    private var cancellables = Set<AnyCancellable>()

    init(
      campaignsListStore: any OGDomainStoreViewStoreConsumable<CampaignsState> = DealsContainer.shared.campaignsListStore()
    ) {
      self.campaignsListStore = campaignsListStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await campaignsListStore
        .watch(keyPath: \.activeCampaigns)
        .removeDuplicates()
        .sink { campaigns in
          Task {
            await dispatch(._setCampaigns(campaigns))
          }
        }
        .store(in: &cancellables)
    }
  }
}
