import AppCore
import OGL10n
import SwiftUI
import UICatalog

// MARK: - DealView.ProductInfoView

extension DealView {
  struct ProductInfoView: View {
    let product: DealProduct

    var body: some View {
      VStack(spacing: UILayoutConstants.Default.padding) {
        if let rating = product.rating {
          RatingView(rating: rating)
        }

        Text(product.title.value)
          .font(for: .copyL)
          .lineLimit(1)

        PriceView(price: product.price)
          .padding(.top, UILayoutConstants.Default.stackSpacing)
      }
      .dynamicTypeSize(...DynamicTypeSize.xLarge)
    }
  }

  // MARK: RatingView

  private struct RatingView: View {
    let rating: DealProduct.Rating
    private let maxStars: Int = 5

    var body: some View {
      HStack(spacing: UILayoutConstants.Default.stackSpacing) {
        HStack(spacing: UILayoutConstants.Default.stackSpacing) {
          ForEach(0 ..< maxStars, id: \.self) { index in
            Image(
              index < rating.numberOfStars ? OGImages.icon16x16RatingOn.name : OGImages.icon16x16RatingOff.name
            )
            .frame(
              width: 16,
              height: 16
            )
          }
        }

        Text("(\(rating.numberOfReviews))")
          .font(for: .copyS)
          .foregroundStyle(OGColors.textOnLight.color)
      }
    }
  }

  // MARK: PriceView

  private struct PriceView: View {
    let price: Price

    private var pricePrefix: String {
      var prefix = ""

      if price.discounted?.boolValue ?? false {
        prefix = ogL10n.Deals.DealVariant.Product.PricePrefix + " "
      }

      if price.priceRange?.boolValue ?? false {
        prefix = ogL10n.Deals.DealVariant.Product.PriceRangePrefix + " "
      }

      return prefix
    }

    private var newPrice: String? {
      NumberFormatter.formattedPrice(
        value: Double(price.value) / 100,
        currency: price.currency
      )
    }

    private var oldPrice: String? {
      guard let oldValue = price.oldValue?.doubleValue else { return nil }
      return NumberFormatter.formattedPrice(
        value: oldValue / 100,
        currency: price.currency
      )
    }

    var body: some View {
      HStack(spacing: 8) {
        if let newPrice {
          Text(pricePrefix + newPrice)
            .font(for: .headlineLEmphasized)
            .foregroundStyle(OGColors.accentSale.color)
        }

        if let oldPrice {
          Text(oldPrice)
            .font(for: .headlineLRegular)
            .foregroundStyle(OGColors.textBlack60.color)
            .strikethroughModifier()
        }
      }
      .lineLimit(1)
      .multilineTextAlignment(.center)
    }
  }
}
