import AppCore
import Combine
import Foundation
import OGDomainStore
import OGL10n
import OGRouter
import OGViewStore
import UICatalog
import UIKit

// MARK: - DealView.Store

extension DealView {
  typealias Store = OGViewStore<DealView.State, DealView.Event>
}

extension DealView {
  @MainActor
  static func make(deal: Deal, index: Int) -> Store {
    DealView.Store(
      initialState: .init(deal: deal, index: index),
      reducer: DealView.Reducer.reduce,
      middleware: DealView.Middleware()
    )
  }
}

// MARK: - DealView.Event

extension DealView {
  enum Event: OGViewEvent {
    case activateDealButtonTapped
    case focusIndexChanged(Int)
    /// Private
    case _trackDealSelected
  }
}

// MARK: - DealView.State

extension DealView {
  struct State: OGViewState {
    let deal: Deal
    let index: Int

    var legalTextCallout: String {
      deal.additionalInfo?.value ?? ""
    }

    var activateDealButtonTitle: String {
      deal.ctaText.value
    }

    var productAccessibilityLabel: String {
      guard let deal = deal as? DealProduct else { return "" }

      let newPrice: String? = NumberFormatter.formattedPrice(
        value: Double(deal.price.value) / 100,
        currency: deal.price.currency
      )

      var oldPrice: String?
      if let oldValue = deal.price.oldValue?.doubleValue {
        oldPrice = NumberFormatter.formattedPrice(
          value: oldValue / 100,
          currency: deal.price.currency
        )
      }

      let productAccessibilityInfo = ogL10n.Deals.DealVariant.Product.Accessibility(
        dealNumber: "\(index + 1)",
        label: deal.label ?? "",
        discount: deal.discount ?? "",
        altText: deal.image.altText ?? "",
        stars: String(deal.rating?.numberOfStars ?? 0),
        voters: String(deal.rating?.numberOfReviews ?? 0),
        title: deal.title.value,
        newPrice: newPrice ?? "",
        oldPrice: oldPrice ?? ""
      )

      return [productAccessibilityInfo, activateDealButtonTitle].joined(separator: ", ")
    }

    init(deal: Deal, index: Int) {
      self.deal = deal
      self.index = index
    }

    static var initial: DealView.State = .init(deal: InititalDeal(), index: 0)
  }
}

extension DealView.State {
  static func == (lhs: DealView.State, rhs: DealView.State) -> Bool {
    lhs.deal.id == rhs.deal.id
  }
}

// MARK: - DealView.Reducer

extension DealView {
  enum Reducer {
    static func reduce(state: inout State, with event: Event) {
      switch event {
      case ._trackDealSelected, .activateDealButtonTapped, .focusIndexChanged:
        break
      }
    }
  }
}

// MARK: - DealView.Middleware

extension DealView {
  final class Middleware: OGViewStoreMiddleware {
    private let promoAccessibilityAnnouncer: AccessibilityAnnouncing
    private let router: OGRoutePublishing
    private let tracker: CampaignTracking

    init(
      promoAccessibilityAnnouncer: AccessibilityAnnouncing = AppContainer.shared.accessibilityAnnouncer(),
      router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher(),
      tracker: CampaignTracking = DealsContainer.shared.campaignTracker()
    ) {
      self.promoAccessibilityAnnouncer = promoAccessibilityAnnouncer
      self.router = router
      self.tracker = tracker
    }

    func callAsFunction(event: Event, for state: State) async -> Event? {
      switch event {
      case .activateDealButtonTapped:
        let route = OGRoute(url: URL(string: state.deal.url))
        router.send(route)
        return ._trackDealSelected

      case ._trackDealSelected:
        await tracker.trackDealSelected(state.deal)
        return nil

      case .focusIndexChanged:
        let announcement = state.deal is DealProduct ? state.productAccessibilityLabel : ogL10n.Deals.DealVariant.Image.Accessibility
        await promoAccessibilityAnnouncer.announceAndDelay(announcement, config: .standard)
        return nil
      }
    }
  }
}
