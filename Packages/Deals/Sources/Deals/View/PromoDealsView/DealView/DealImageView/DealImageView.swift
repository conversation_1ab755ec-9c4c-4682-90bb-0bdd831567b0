import AppCore
import OGAsyncImage
import OGL10n
import SwiftUI
import UICatalog

// MARK: - DealImageView

struct DealImageView: View {
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var isCodeCopied: Bool = false

  @Environment(\.screenSize) private var screenSize
  @EnvironmentObject private var deviceState: DeviceState

  init(deal: Deal, index: Int, dealsCount: Int) {
    _viewStore = StateObject(
      wrappedValue: Self.make(deal: deal, index: index, dealsCount: dealsCount)
    )
  }

  var body: some View {
    ZStack(alignment: .bottom) {
      ZStack(alignment: .bottomLeading) {
        OGAsyncImage(
          url: URL(string: viewStore.imageURL),
          frame: .init(isEnabled: true, alignment: .center),
          fallbackView: { fallbackImageView }
        )
        .roundedCorners(
          .allCorners,
          radius: UILayoutConstants.DealView.dealCornerRadius
        )

        if let discount = viewStore.discount {
          DiscountLabel(discount: discount)
            .padding(UILayoutConstants.Default.padding2x)
        }
      }
      .accessibilityElement(children: .ignore)
      .if(!viewStore.isProduct) { view in
        view
          .accessibilityLabel(viewStore.promotionAccessibilityLabel)
          .accessibilityAddTraits(.isImage)
      }

      if let code = viewStore.code {
        PromoCodeView(
          code: code,
          style: .default,
          isCodeCopied: $isCodeCopied
        ) {
          Task {
            await viewStore.dispatch(.copyCodeButtonTapped)
          }
        }
      }
    }
    .aspectRatio(UILayoutConstants.DealView.dealImageAspectRatio, contentMode: .fit)
    .frame(maxWidth: adjustedMaxWidth)
  }

  private var adjustedMaxWidth: CGFloat {
    let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
    return screenSize * UILayoutConstants.DealView.dealImageAspectRatio
  }

  private var fallbackImageView: some View {
    OGColors.backgroundBackground10.color
      .overlay {
        OGImages.icon24x24PlaceholderImg.image
      }
  }
}

// MARK: DealImageView.DiscountLabel

extension DealImageView {
  private struct DiscountLabel: View {
    var discount: String

    var body: some View {
      Text(discount)
        .font(for: .headlineXL)
        .foregroundColor(OGColors.textOnDark.color)
        .padding(UILayoutConstants.Default.padding)
        .background(
          RoundedRectangle(cornerRadius: UILayoutConstants.Default.padding)
            .fill(OGColors.accentSale.color)
        )
    }
  }
}
