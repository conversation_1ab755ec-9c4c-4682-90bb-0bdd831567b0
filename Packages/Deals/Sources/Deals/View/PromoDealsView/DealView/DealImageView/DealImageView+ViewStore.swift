import Foundation
import OGL10n
import OGViewStore

// MARK: - DealImageView.Store

extension DealImageView {
  typealias Store = OGViewStore<DealImageView.State, DealImageView.Event>
}

extension DealImageView {
  @MainActor
  static func make(deal: Deal, index: Int, dealsCount: Int) -> Store {
    DealImageView.Store(
      initialState: .init(deal: deal, index: index, dealsCount: dealsCount),
      reducer: DealImageView.Reducer.reduce,
      middleware: DealImageView.Middleware()
    )
  }
}

// MARK: - DealImageView.Event

extension DealImageView {
  enum Event: OGViewEvent {
    case copyCodeButtonTapped
  }
}

// MARK: - DealImageView.State

extension DealImageView {
  struct State: OGViewState {
    let deal: Deal
    let index: Int
    let dealsCount: Int

    var code: String? {
      guard
        let deal = deal as? DealCode
      else { return nil }
      return deal.code
    }

    var imageURL: String {
      deal.image.url
    }

    var isProduct: Bool {
      deal is DealProduct
    }

    var discount: String? {
      guard let deal = deal as? DealProduct, let discount = deal.discount else { return nil }
      return discount
    }

    var promotionAccessibilityLabel: String {
      let selectionLabel = ogL10n.General.SegmentedControl.SelectionPosition.Accessibility(
        itemPosition: String(describing: index + 1),
        totalItems: String(describing: dealsCount)
      )
      let imageAccessibility = ogL10n.Deals.DealVariant.Image.Accessibility
      return [selectionLabel, imageAccessibility].joined(separator: ", ")
    }

    init(deal: Deal, index: Int, dealsCount: Int) {
      self.deal = deal
      self.index = index
      self.dealsCount = dealsCount
    }

    static var initial: DealImageView.State = .init(deal: InititalDeal(), index: 0, dealsCount: 0)
  }
}

extension DealImageView.State {
  static func == (lhs: DealImageView.State, rhs: DealImageView.State) -> Bool {
    lhs.deal.id == rhs.deal.id
  }
}

// MARK: - DealImageView.Reducer

extension DealImageView {
  enum Reducer {
    static func reduce(state: inout State, with event: Event) {}
  }
}

// MARK: - DealImageView.Middleware

extension DealImageView {
  final class Middleware: OGViewStoreMiddleware {
    private let tracker: CampaignTracking

    init(
      tracker: CampaignTracking = DealsContainer.shared.campaignTracker()
    ) {
      self.tracker = tracker
    }

    func callAsFunction(event: Event, for state: State) async -> Event? {
      switch event {
      case .copyCodeButtonTapped:
        await tracker.trackCodeCopied(for: .deal)
        return nil
      }
    }
  }
}
