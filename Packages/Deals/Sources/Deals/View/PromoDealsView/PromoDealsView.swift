import AppCore
import OGL10n
import SwiftUI
import UICatalog

// MARK: - PromoDealsView

struct PromoDealsView: View {
  @Environment(\.screenSize) private var screenSize
  @EnvironmentObject private var deviceState: DeviceState

  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var focusedItemIndex: Int = 0
  @SwiftUI.State private var showMotionSettingSheet: Bool = false

  init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  var body: some View {
    ScrollView(showsIndicators: false) {
      VStack(spacing: UILayoutConstants.Default.padding2x) {
        if let timeLeft = viewStore.dealEndTimeLeft {
          CountdownView(
            dealsTimeLeft: timeLeft,
            timerTitle: viewStore.timerTitle
          )
          .accessibilitySortPriority(3)
        }

        ZStack(alignment: .top) {
          DealsList(
            deals: viewStore.dealList,
            focusedItemIndex: $focusedItemIndex
          )
          .frame(width: screenSize.width)
          .dynamicTypeSize(...DynamicTypeSize.xxxLarge)

          if viewStore.dealList.count > 1 {
            PageNavigator(
              totalPages: viewStore.dealList.count,
              currentPageIndex: $focusedItemIndex
            )
            .offset(y: pageNavigatorOffset)
            .if(deviceState.isLandscapeOrientation) {
              $0.frame(width: screenSize.height + UILayoutConstants.Default.padding2x)
            }
            .accessibilityHidden(true)
          }
        }
        .accessibilitySortPriority(2)
        .if(viewStore.dealList.count > 1) { view in
          view
            .accessibilityElement(children: .contain)
            .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: "\(viewStore.dealList.count)"))
        }

        Spacer()
      }
      .onChange(of: focusedItemIndex) { _ in
        Task {
          await viewStore.dispatch(.dealSwiped)
        }
      }
    }
    .disableScrollBounce()
  }

  private var pageNavigatorOffset: CGFloat {
    let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
    return screenSize / 2 - UILayoutConstants.DefaultButton.height / 2
  }
}

// MARK: PromoDealsView.CountdownView

extension PromoDealsView {
  struct CountdownView: View {
    let dealsTimeLeft: DaysInterval
    let timerTitle: String

    var body: some View {
      VStack(spacing: UILayoutConstants.Default.padding) {
        Text(timerTitle)
          .font(for: .copyL)
          .foregroundStyle(OGColors.textOnLight.color)
        Text(dealsTimeLeft.value)
          .font(for: .headlineXXXL)
          .foregroundStyle(OGColors.textOnLight.color)
          .multilineTextAlignment(.center)
      }
      .padding(.top, UILayoutConstants.Default.padding2x)
      .accessibilityElement(children: .combine)
      .accessibilityLabel(timerTitle + ", " + dealsTimeLeft.accessibilityLabel)
      .dynamicTypeSize(...DynamicTypeSize.xxLarge)
    }
  }
}

// MARK: PromoDealsView.DealsList

extension PromoDealsView {
  struct DealsList: View {
    @EnvironmentObject private var deviceState: DeviceState
    @Environment(\.screenSize) private var screenSize

    private let deals: [Deal]
    @Binding private var focusedItemIndex: Int

    init(
      deals: [Deal],
      focusedItemIndex: Binding<Int>
    ) {
      self.deals = deals
      self._focusedItemIndex = focusedItemIndex
    }

    var body: some View {
      ZStack(alignment: .top) {
        ForEach(0 ..< deals.count, id: \.self) { index in
          DealView(
            deal: deals[index],
            isFocused: index == focusedItemIndex,
            index: index,
            focusedAccessibilityIndex: $focusedItemIndex,
            dealsCount: deals.count
          )
          .offset(
            x: getOffset(for: index),
            y: 0
          )
          .accessibilityHidden(index < focusedItemIndex - 1 || index > focusedItemIndex + 1)
        }
      }
      .simultaneousGesture(dragGesture)
      .if(deals.count > 1) {
        $0.accessibilityAdjustableAction { direction in
          switch direction {
          case .increment:
            increaseFocusedItemIndex()
          case .decrement:
            decreaseFocusedItemIndex()
          default:
            break
          }
        }
      }
    }

    private var dragGesture: some Gesture {
      DragGesture()
        .onEnded { value in
          let threshold: CGFloat = 50
          if value.translation.width > threshold {
            increaseFocusedItemIndex()
          } else if value.translation.width < -threshold {
            decreaseFocusedItemIndex()
          }
        }
    }

    private func increaseFocusedItemIndex() {
      withAnimation {
        focusedItemIndex = max(0, focusedItemIndex - 1)
      }
    }

    private func decreaseFocusedItemIndex() {
      withAnimation {
        focusedItemIndex = min(deals.count - 1, focusedItemIndex + 1)
      }
    }

    private func getOffset(for index: Int) -> CGFloat {
      let indexOffset = CGFloat(index - focusedItemIndex)
      let screenSize = deviceState.isLandscapeOrientation ? screenSize.height : screenSize.width
      let widthOffset = screenSize * UILayoutConstants.DealView.dealImageAspectRatio + UILayoutConstants.Default.padding2x
      return indexOffset * widthOffset
    }
  }
}
