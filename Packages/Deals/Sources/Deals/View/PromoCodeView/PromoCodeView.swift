import SwiftUI
import UICatalog

// MARK: - CodeButtonStyle

enum CodeButtonStyle {
  case dashedBorder
  case `default`

  var codeFont: OGFonts {
    switch self {
    case .dashedBorder:
      return OGFonts.titleS
    case .default:
      return OGFonts.headlineLEmphasized
    }
  }

  var font: OGFonts {
    switch self {
    case .dashedBorder:
      return OGFonts.copyMRegular
    case .default:
      return OGFonts.headlineLRegular
    }
  }
}

// MARK: - PromoCodeView

struct PromoCodeView: View {
  @StateObject private var viewStore: Self.Store
  @Binding private var isCodeCopied: Bool
  private(set) var onTap: (() -> Void)?
  private let style: CodeButtonStyle

  @SwiftUI.State private var isAnimating = false

  init(
    code: String,
    style: CodeButtonStyle,
    isCodeCopied: Binding<Bool>? = nil,
    onTap: (() -> Void)? = nil
  ) {
    _viewStore = StateObject(wrappedValue: Self.make(code: code))
    _isCodeCopied = isCodeCopied ?? .constant(false)
    self.style = style
    self.onTap = onTap
  }

  var body: some View {
    Button(action: handleButtonTap) {
      if viewStore.isCodeCopied {
        copiedStatusContent
      } else {
        copyButtonContent
      }
    }
    .disabled(viewStore.isCodeCopied)
    .applyStyle(style, isCodeCopied: viewStore.isCodeCopied)
    .foregroundStyle(OGColors.textOnDark.color)
    .tint(OGColors.textOnDark.color)
    .onChange(of: viewStore.isCodeCopied) { isCodeCopied in
      self.isCodeCopied = isCodeCopied
    }
    .transition(.opacity)
    .animation(.easeInOut(duration: 0.5), value: viewStore.isCodeCopied)
    .accessibilityElement(children: .ignore)
    .accessibilityLabel(viewStore.copyAccessibilityLabel)
    .accessibilityAddTraits(.isButton)
    .accessibilityAction {
      handleButtonTap()
    }
    .dynamicTypeSize(...DynamicTypeSize.xLarge)
  }

  private func handleButtonTap() {
    onTap?()

    Task {
      await viewStore.dispatch(.codeButtonTapped)
    }
  }
}

// MARK: - Subviews

extension PromoCodeView {
  @ViewBuilder private var copyButtonContent: some View {
    contentView(for: viewStore.copyButtonText)
  }

  @ViewBuilder private var copiedStatusContent: some View {
    contentView(for: viewStore.copiedMessage)
  }

  @ViewBuilder
  private func contentView(for text: String) -> some View {
    HStack(alignment: .bottom, spacing: UILayoutConstants.Default.stackSpacing) {
      Text(formattedPromoCodeText(from: text))
        .font(for: style.font)
        .multilineTextAlignment(.center)
        .lineLimit(2)

      statusIcon
    }
    .if(style == .default) { view in
      view
        .frame(maxWidth: .infinity)
        .padding(.vertical, UILayoutConstants.Default.padding)
    }
    .accessibilityElement(children: .ignore)
  }

  private var statusIcon: some View {
    ZStack {
      if viewStore.isCodeCopied {
        OGImages.icon24x24CheckmarkOnDark.image
      } else {
        OGImages.icon24x24Copy.image
      }
    }
    .frame(
      width: UILayoutConstants.PromoCodeView.copyIconSize,
      height: UILayoutConstants.PromoCodeView.copyIconSize
    )
  }

  private func formattedPromoCodeText(from text: String) -> AttributedString {
    let attributedString = NSMutableAttributedString(string: text)
    let fontStyle = FontResolverContainer.shared.fontResolver().font(style.codeFont)
    let font = UIFont(
      name: fontStyle.name,
      size: CGFloat(fontStyle.size)
    ) ?? UIFont.systemFont(ofSize: 18, weight: .bold)

    let boldRange = (attributedString.string as NSString).range(of: viewStore.code)
    attributedString.addAttribute(.font, value: font, range: boldRange)

    return AttributedString(attributedString)
  }
}

// MARK: - View Modifiers

extension View {
  fileprivate func applyStyle(_ style: CodeButtonStyle, isCodeCopied: Bool) -> some View {
    Group {
      switch style {
      case .dashedBorder:
        self.buttonStyle(
          DashedBorderButtonStyle(
            padding: (
              vertical: UILayoutConstants.Default.stackSpacing,
              horizontal: UILayoutConstants.Default.padding
            ),
            cornerRadius: UILayoutConstants.Default.padding,
            strokeStyle: StrokeStyle(
              lineWidth: 1,
              miterLimit: 3,
              dash: [5, 3],
              dashPhase: 3
            )
          )
        )
      case .default:
        self.background(
          Rectangle()
            .fill(
              isCodeCopied ? OGColors.accentSuccess.color : OGColors.accentSale.color
            )
            .roundedCorners(
              [.bottomLeft, .bottomRight],
              radius: UILayoutConstants.DealView.dealCornerRadius
            )
        )
      }
    }
  }
}

// MARK: - UILayoutConstants.PromoCodeView

extension UILayoutConstants {
  enum PromoCodeView {
    static let copyIconSize: CGFloat = 24.0
  }
}
