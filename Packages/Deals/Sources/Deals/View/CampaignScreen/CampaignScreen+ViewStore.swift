import AppCore
import Combine
import Foundation
import OGDomainStore
import OGL10n

import OGViewStore

// MARK: - CampaignScreen.Store

extension CampaignScreen {
  typealias Store = OGViewStore<State, Event>
}

extension CampaignScreen {
  @MainActor
  static func make(campaignId: CampaignIdHolder) -> Store {
    Store(
      reducer: Reducer.reduce,
      middleware: Middleware(),
      connector: Connector(campaignId: campaignId)
    )
  }
}

// MARK: - CampaignScreen.Event

extension CampaignScreen {
  enum Event: OGViewEvent {
    case fetch
    /// Private events
    case _error
    case _updated
    case _setGamePlayedToday(Bool?)
    case _trackPromoDealsDidAppear
    case _trackGameDidAppear
    case _setCampaign(Campaign)
    case _setSelectedCampignId(CampaignIdHolder)
  }
}

// MARK: - CampaignScreen.State

extension CampaignScreen {
  struct State: OGViewState {
    private(set) var loadableState: LoadableState
    private(set) var isGamePlayedToday: Bool?
    private(set) var campaign: Campaign?

    var game: Game? {
      campaign?.game
    }

    var blurBackgroundRequired: Bool {
      guard
        let game,
        let gameBackgrounds = game.background,
        let isGamePlayedToday
      else { return false }
      let hasBlurFilter = !gameBackgrounds.filter { $0 is AssetFilterBlur }.isEmpty
      return !isGamePlayedToday && hasBlurFilter
    }

    init(
      loadableState: LoadableState = .loading
    ) {
      self.loadableState = loadableState
    }

    mutating func update(
      loadableState: LoadableState? = nil,
      isGamePlayedToday: Bool? = nil,
      campaign: Campaign? = nil
    ) {
      self.loadableState = loadableState ?? self.loadableState
      self.isGamePlayedToday = isGamePlayedToday ?? self.isGamePlayedToday
      self.campaign = campaign ?? self.campaign
    }

    static var initial: CampaignScreen.State = .init()
  }
}

extension CampaignScreen {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout State,
      with event: Event
    ) {
      switch event {
      case ._error:
        state.update(loadableState: .error)

      case .fetch:
        state.update(loadableState: .loading)

      case ._updated:
        state.update(loadableState: .loaded)

      case let ._setGamePlayedToday(isGamePlayedToday):
        state.update(isGamePlayedToday: isGamePlayedToday)

      case let ._setCampaign(campaign):
        state.update(campaign: campaign)

      case ._setSelectedCampignId, ._trackGameDidAppear, ._trackPromoDealsDidAppear:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let tracker: CampaignTracking
    private let campaignStore: any OGDomainActionDetachedDispatchable
    private let campaignsListStore: any OGDomainActionDetachedDispatchable

    init(
      campaignStore: any OGDomainActionDetachedDispatchable = DealsContainer.shared.campaignStore(),
      campaignsListStore: any OGDomainActionDetachedDispatchable = DealsContainer.shared.campaignsListStore(),
      tracker: CampaignTracking = DealsContainer.shared.campaignTracker()
    ) {
      self.campaignStore = campaignStore
      self.campaignsListStore = campaignsListStore
      self.tracker = tracker
    }

    func callAsFunction(
      event: Event,
      for state: State
    ) async -> Event? {
      switch event {
      case let ._setSelectedCampignId(campaignId):
        campaignsListStore.dispatchDetached(CampaignsAction.setSelectedCampaignId(campaignId))
        return nil

      case .fetch:
        if let campaignId = state.campaign?.id {
          let idHolder = CampaignIdHolder(id: .init(value: campaignId))
          campaignStore.dispatchDetached(CampaignAction.getCampaign(idHolder))
        }
        return nil

      case ._trackGameDidAppear:
        await tracker.trackGameDidAppear()
        return nil

      case ._trackPromoDealsDidAppear:
        await tracker.trackPromoDealsDidAppear()
        return nil

      case let ._setGamePlayedToday(isPlayedToday):
        guard let isPlayedToday else { return nil }
        if !isPlayedToday {
          return ._trackGameDidAppear
        } else {
          return ._trackPromoDealsDidAppear
        }

      case let ._setCampaign(campaign):
        if campaign.deals.items?.isEmpty ?? true {
          return ._error
        }

        return ._updated

      case ._error, ._updated:
        return nil
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let campaignId: CampaignIdHolder
    private let campaignStore: any OGDomainStoreViewStoreConsumable<CampaignState>
    private var cancellables = Set<AnyCancellable>()

    init(
      campaignId: CampaignIdHolder,
      campaignStore: any OGDomainStoreViewStoreConsumable<CampaignState> = DealsContainer.shared.campaignStore()
    ) {
      self.campaignId = campaignId
      self.campaignStore = campaignStore
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      await dispatch(._setSelectedCampignId(campaignId))

      await campaignStore
        .watch(keyPath: \.campaign)
        .compactMap { $0 }
        .removeDuplicates()
        .sink { campaign in
          Task {
            await dispatch(._setCampaign(campaign))
          }
        }
        .store(in: &cancellables)

      await campaignStore
        .watch(keyPath: \.isGamePlayedToday)
        .compactMap { $0 }
        .removeDuplicates()
        .sink { isGamePlayedToday in
          Task {
            await dispatch(._setGamePlayedToday(isGamePlayedToday))
          }
        }
        .store(in: &cancellables)

      await campaignStore
        .watchError(type: CampaignError.self)
        .sink { _ in
          Task {
            await dispatch(._error)
          }
        }
        .store(in: &cancellables)
    }
  }
}
