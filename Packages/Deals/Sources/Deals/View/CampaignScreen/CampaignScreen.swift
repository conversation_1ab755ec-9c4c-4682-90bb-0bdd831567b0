import AppCore
import OGAsyncImage
import OGIdentifier
import OGNavigationBar
import Swift<PERSON>
import UICatalog

// MARK: - CampaignScreen

struct CampaignScreen: View {
  @StateObject private var viewStore: Self.Store

  @SwiftUI.State private var blurBackgroundRequired: Bool = false
  @SwiftUI.State private var showGameView: Bool = false
  @SwiftUI.State private var showDealsView: Bool = false

  private let title: String

  init(title: String, campaignId: CampaignIdHolder) {
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.make(campaignId: campaignId))
  }

  var body: some View {
    OGNavigationBar(titleView: navigationBarTitle) {
      Group {
        switch viewStore.loadableState {
        case .loading:
          loadingView
        case .loaded:
          loadedView
        case .error:
          errorView
        }
      }
    }
    .onChange(
      of: viewStore.isGamePlayedToday,
      perform: handleGamePlayedChange
    )
    .onChange(
      of: viewStore.blurBackgroundRequired,
      perform: handleBlurBackgroundChange
    )
  }

  private var navigationBarTitle: some View {
    Text(title)
  }

  private var loadingView: some View {
    Color.white
      .overlay(
        ProgressView()
      )
  }

  private var loadedView: some View {
    ZStack {
      if showDealsView {
        PromoDealsView()
          .disabled(blurBackgroundRequired)
          .if(blurBackgroundRequired) { view in
            view
              .overlay {
                OGColors.backgroundBackground020.color
              }
              .blur(radius: UILayoutConstants.CampaignScreen.blurRadius)
          }
          .frame(maxWidth: .infinity)
          .accessibilityHidden(showGameView)
      }

      if showGameView {
        GeometryReader { geometry in
          ScrollView(showsIndicators: false) {
            GameView()
              .frame(maxWidth: .infinity, minHeight: geometry.size.height)
          }
          .compositingGroup()
          .frame(width: geometry.size.width, height: geometry.size.height)
          .disableScrollBounce()
        }
      }
    }
    .transition(.opacity)
  }

  private var errorView: some View {
    CampaignScreenErrorView(viewStore: viewStore)
  }

  private func handleGamePlayedChange(isGamePlayedToday: Bool?) {
    withAnimation {
      guard let isGamePlayedToday else { return }

      if isGamePlayedToday {
        showDealsView = true
        showGameView = false
      } else {
        showGameView = true
        DispatchQueue.main.asyncAfter(deadline: .now() + .milliseconds(500)) {
          showDealsView = true
        }
      }
    }
  }

  private func handleBlurBackgroundChange(newValue: Bool) {
    withAnimation {
      blurBackgroundRequired = newValue
    }
  }
}

// MARK: - UILayoutConstants.CampaignScreen

extension UILayoutConstants {
  enum CampaignScreen {
    static let blurRadius: CGFloat = 32.0
  }
}
