import OGDIService
import OGDomainStore
import OGFeatureAdapter
import OGL10n
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - DealsDestinationProvider

public struct DealsDestinationProvider: OGDestinationProvidable {
  public let identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.deals
  }

  public func provide(_ route: OGRoute) -> some View {
    if let campaignId = route.url.path.split(separator: "/").first {
      let id = String(campaignId)
      let campaignIdHolder = CampaignIdHolder(id: Identifier(value: id))
      viewWithTitle { title in
        CampaignScreen(title: title, campaignId: campaignIdHolder)
      }
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.Deals.GameScreen.NavigationTitle
  }
}
