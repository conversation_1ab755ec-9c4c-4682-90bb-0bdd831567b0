// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "ExternalDependencies",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "ExternalDependencies",
      targets: ["ExternalDependencies"]
    )
  ],
  dependencies: [
    .package(
      url: "**************:aacml/og-dx_aac-ios-module-otto_group_commerce_kit.git",
      branch: "OGAK-3736-WIT-PoC"
    ),
    .package(
      url: "https://github.com/marksands/BetterCodable",
      exact: "0.4.0"
    ),
    .package(
      url: "https://github.com/airbnb/lottie-ios",
      from: "4.5.0"
    )
  ],
  targets: [
    .target(
      name: "ExternalDependencies",
      dependencies: [
        .product(
          name: "OGKit",
          package: "og-dx_aac-ios-module-otto_group_commerce_kit"
        ),
        "BetterCodable",
        .product(
          name: "<PERSON><PERSON>",
          package: "lottie-ios"
        )
      ]
    ),
    .testTarget(
      name: "ExternalDependenciesTests",
      dependencies: ["ExternalDependencies"]
    )
  ]
)
