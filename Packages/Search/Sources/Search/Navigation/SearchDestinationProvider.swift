import OGFeatureAdapter
import OGMacros
import OGNavigation
import <PERSON><PERSON>

// MARK: - SearchDestinationProvider

public struct SearchDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .search

  public init() {}

  public func provide(_ route: OGRoute) -> some View {
    SearchView()
      .navigationBarHidden(true)
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGRoute {
  public static let search = OGRoute(OGIdentifier.search.value)
}
