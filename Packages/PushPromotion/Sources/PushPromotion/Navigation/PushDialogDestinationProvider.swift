import Foundation
import OGMacros
import OGNavigation
import SwiftUI

// MARK: - PushDialogDestinationProvider

public struct PushDialogDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.pushDialog
  }

  public func provide(_ route: OGRoute) -> some View {
    PushNotificationView()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGIdentifier {
  public static let pushDialog = #identifier("pushDialog")
}
