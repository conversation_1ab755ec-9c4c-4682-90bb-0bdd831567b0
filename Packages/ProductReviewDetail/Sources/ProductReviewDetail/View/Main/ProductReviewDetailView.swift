import OGDIService
import OGL10n
import OGNavigationBar
import O<PERSON>outer
import SwiftUI
import UICatalog

// MARK: - ProductReviewDetailView

struct ProductReviewDetailView: View {
  @StateObject private var viewStore: Self.Store
  @State private var isSortSheetPresented: Bool = false
  @OGInjected(\ProductReviewDetailContainer.buttonStyleResolver) private var buttonStyleResolver

  private let title: String

  init(title: String, route: OGRoute) {
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.makeStore(route: route))
  }

  var body: some View {
    OGNavigationBar(titleView: titleView) {
      content
    }
  }

  private var content: some View {
    Group {
      switch viewStore.loadingState {
      case .initial, .progress:
        ProgressView()
      case .failure:
        EmptyView()
      case .success:
        reviewContent
        writeReviewButtonView
      }
    }
    .onAppear {
      Task {
        await viewStore.dispatch(.trackScreenView)
      }
    }
  }

  @ViewBuilder private var writeReviewButtonView: some View {
    if viewStore.hasWriteReviewButton {
      VStack(spacing: .zero) {
        Divider()
          .foregroundColor(OGColors.backgroundBackground20.color)
        C2AButton(
          title: ogL10n.ProductDetail.Review.NewReview.Button.Title,
          accessibilityIdentifier: ogL10n.ProductDetail.Review.NewReview.Button.Title
        ) {
          Task {
            await viewStore.dispatch(.writeReviewButtonTapped)
          }
        }
        .accessibilityLabel(ogL10n.ProductDetail.Basket.Button.Title.Accessibility)
        .register(buttonStyleResolver.primaryButtonStyle)
        .frame(maxWidth: .infinity)
        .padding(.vertical, UILayoutConstants.ProductReviewDetailView.writeReviewVerticalPadding)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .dynamicTypeSize(...DynamicTypeSize.accessibility1)
      }
      .frame(minHeight: UILayoutConstants.ProductReviewDetailView.writeReviewOverlayHeight)
    }
  }

  private var titleView: some View {
    Text(title)
      .font(for: .titleM)
      .foregroundColor(OGColors.navigationBarElementTitle.color)
      .dynamicTypeSize(...DynamicTypeSize.xLarge)
      .accessibilityAddTraits(.isHeader)
  }

  private var reviewContent: some View {
    ScrollViewReader { reader in
      ScrollView {
        VStack(alignment: .center, spacing: UILayoutConstants.ProductReview.verticalSpacing) {
          ForEach(viewStore.components, id: \.self) { component in
            viewForComponent(component: component)
          }
        }
        .onChange(of: viewStore.reviewIndex) { newValue in
          withAnimation(.easeInOut) {
            reader.scrollTo(newValue, anchor: .top)
          }
        }
      }
    }
    .sheet(isPresented: $isSortSheetPresented) {
      SortOptionsSheet(screenId: viewStore.reviewsSortingOptions?.screenId ?? "") {
        isSortSheetPresented = false
      }
    }
  }

  @ViewBuilder
  private func viewForComponent(component: OGProductReviewDetailComponent) -> some View {
    switch component {
    case let .reviewsInformation(reviewInfo):
      ProductSummaryView(reviewInfo: reviewInfo)
        .padding(.top, UILayoutConstants.Default.padding3x)
        .padding(.bottom, UILayoutConstants.Default.padding)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .frame(maxWidth: UILayoutConstants.ProductReviewDetailView.maxWidth, alignment: .leading)
      RatingFilterView(ratingDistribution: reviewInfo.rating?.ratingDistribution, screenId: reviewInfo.screenId)
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .frame(maxWidth: UILayoutConstants.ProductReviewDetailView.maxWidth, alignment: .leading)

    case let .reviewsList(reviewsList):
      ReviewContentView(reviews: reviewsList.reviews, expandedReviewIndex: viewStore.reviewIndex)
        .padding(.leading, UILayoutConstants.Default.padding2x)
        .padding(.bottom, UILayoutConstants.Default.padding2x)
      if reviewsList.reviews.count < reviewsList.totalReviewsCount {
        PaginationView(
          displayedCount: reviewsList.reviews.count,
          totalReviews: Int(reviewsList.totalReviewsCount),
          screenId: reviewsList.screenId
        )
        .padding(.horizontal, UILayoutConstants.Default.padding2x)
        .padding(.bottom, UILayoutConstants.Default.padding2x)
      }
    case let .reviewsSortingOptions(reviewsSortingOptions):
      FilterSortView(screenId: reviewsSortingOptions.screenId) {
        isSortSheetPresented = true
      }
      .padding(.vertical, UILayoutConstants.Default.padding2x)
      .padding(.horizontal, UILayoutConstants.Default.padding2x)
      .frame(maxWidth: UILayoutConstants.ProductReviewDetailView.maxWidth)
    case .writeReviewButton:
      EmptyView()
    }
  }
}

// MARK: - UILayoutConstants.ProductReviewDetailView

extension UILayoutConstants {
  enum ProductReviewDetailView {
    static let maxWidth: CGFloat = 512
    static let writeReviewOverlayHeight: CGFloat = 72
    static let writeReviewVerticalPadding: CGFloat = 12
    static let summeryHorizontalPadding: CGFloat = 10
  }
}
