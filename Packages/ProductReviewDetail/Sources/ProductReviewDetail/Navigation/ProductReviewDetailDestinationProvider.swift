import OGAppKitSDK
import OGCore
import OGL10n
import OGRouter
import SwiftUI

// MARK: - ProductReviewDetailDestinationProvider

public struct ProductReviewDetailDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.productReviewDetail
  }

  public func provide(_ route: OGRoute) -> some SwiftUI.View {
    viewWithTitle { title in
      ProductReviewDetailView(title: title, route: route)
    }
  }

  public func title(for _: OGRoute?) -> String? {
    ogL10n.ProductReview.Title
  }
}

extension OGRoute {
  public static let productReviewDetail = OGRoute(OGIdentifier.productReviewDetail.value)
}
