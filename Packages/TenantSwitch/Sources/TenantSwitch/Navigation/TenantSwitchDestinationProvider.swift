import OGIdentifier
import OGL10n
import OGNavigation
import SwiftUI

public struct TenantSwitchDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = .tenantSwitch
  }

  public func provide(_ route: OGRoute) -> some View {
    TenantSwitchAlertView(route: route)
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
