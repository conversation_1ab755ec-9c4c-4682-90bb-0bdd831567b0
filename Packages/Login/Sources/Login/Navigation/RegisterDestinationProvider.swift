import OGNavigation
import SwiftUI

// MARK: - RegisterDestinationProvider

public struct RegisterDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.register
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle(for: route) { title in
      RegisterView(title: title, route: route)
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }

  public func title(for route: OGRoute?) -> String? {
    let title: OGRouteTitle? = route?.getData()
    return title?.value ?? ""
  }
}
