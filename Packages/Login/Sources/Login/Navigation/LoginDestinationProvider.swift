import OGNavigation
import SwiftUI

// MARK: - LoginDestinationProvider

public struct LoginDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.login
  }

  public func provide(_ route: OGRoute) -> some View {
    viewWithTitle(for: route) { title in
      LoginView(title: title, route: route)
    }
  }

  public func presentationType() -> OGPresentationType {
    .sheet([.large])
  }

  public func title(for route: OGRoute?) -> String? {
    let title: OGRouteTitle? = route?.getData()
    return title?.value ?? ""
  }
}
