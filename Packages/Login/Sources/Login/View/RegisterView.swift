import OGDIService

import OGNavigationBar
import OGRouter
import OGWebView
import SwiftUI
import UICatalog
import WebKit

// MARK: - RegisterView

struct RegisterView: View {
  @StateObject private var viewStore: RegisterView.Store
  private let title: String
  private let route: OGRoute

  init(title: String, route: OGRoute) {
    self.title = title
    self.route = route
    _viewStore = StateObject(wrappedValue: Self.make(route: route))
  }

  private var requestBinding: Binding<URLRequest> {
    Binding(
      get: { viewStore.request },
      set: { newValue in
        Task {
          await viewStore.dispatch(.setRequest(newValue))
        }
      }
    )
  }

  var body: some View {
    BaseAuthView(
      title: title,
      route: route,
      backButtonAction: {
        Task {
          await viewStore.dispatch(.dismiss)
        }
      }, request: requestBinding
    )
  }
}
