import OGDIService
import OGL10n
import OGNavigationBar
import <PERSON>GRouter
import OGWebView
import SwiftUI
import UICatalog
import WebKit

struct BaseAuthView: View {
  @StateObject private var webViewStore: OGWebViewStore
  @Environment(\.dismiss) private var dismiss
  private let route: OGRoute
  private let backButtonAction: () -> Void
  @Binding var request: URLRequest

  private let title: String

  init(
    title: String,
    route: OGRoute,
    backButtonAction: @escaping () -> Void,
    request: Binding<URLRequest>
  ) {
    self.title = title
    self.route = route
    self.backButtonAction = backButtonAction
    self._webViewStore = StateObject(wrappedValue: OGWebViewStore(route: route))
    self._request = request
  }

  var body: some View {
    OGNavigationBar(
      leadingView: backButton,
      titleView: header,
      trailingView: closeButton,
      hasBackButton: OGObservedBoolean(false)
    ) {
      content
    }
    .onChange(of: request) { value in
      webViewStore.update(request: value)
    }
  }

  private var header: some View {
    Text(title)
      .padding(.top)
  }

  @ViewBuilder private var content: some View {
    OGWebViewProgressView(estimatedProgress: $webViewStore.estimatedProgress)
    OGWebView(with: webViewStore.webView)
  }

  @ViewBuilder private var backButton: some View {
    OGWebViewBackButton {
      backButtonAction()
    }
    .padding(.top)
    .accessibilityLabel(ogL10n.General.Back)
  }

  @ViewBuilder private var closeButton: some View {
    Button {
      dismiss()
    } label: {
      Text(ogL10n.General.Cancel)
        .foregroundStyle(OGColors.tabBarElementActive.color)
        .font(for: .buttonLabelM)
    }
    .padding([.top, .trailing])
    .fixedSize(horizontal: true, vertical: false)
  }
}
