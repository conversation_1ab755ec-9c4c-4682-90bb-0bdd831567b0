import AVKit
import Combine
import OGDIService
import SwiftUI

// MARK: - PlayerSource

public enum PlayerSource {
  case player(AVPlayer?)
  case url(URL)

  var player: AVPlayer? {
    switch self {
    case let .player(avPlayer):
      return avPlayer
    case let .url(url):
      return AVPlayer(url: url)
    }
  }

  var url: URL? {
    switch self {
    case let .player(avPlayer):
      return (avPlayer?.currentItem?.asset as? AVURLAsset)?.url
    case let .url(url):
      return url
    }
  }
}

// MARK: - ContentView

private struct ContentView: UIViewControllerRepresentable {
  private let notificationCenter: NotificationCenter
  private var player: AVPlayer?
  private let allowsPictureInPicturePlayback: Bool
  private let showsPlaybackControls: Bool

  @Binding var isPlaying: Bool

  init(
    source: PlayerSource,
    isPlaying: Binding<Bool>,
    notificationCenter: NotificationCenter,
    allowsPictureInPicturePlayback: Bool,
    showsPlaybackControls: Bool
  ) {
    self._isPlaying = isPlaying
    self.notificationCenter = notificationCenter
    self.allowsPictureInPicturePlayback = allowsPictureInPicturePlayback
    self.showsPlaybackControls = showsPlaybackControls
    self.player = source.player
  }

  mutating func cleaup() {
    player?.pause()
    player = nil
  }

  func makeUIViewController(context: Context) -> AVPlayerViewController {
    let playerViewController = AVPlayerViewController()
    playerViewController.player = player
    playerViewController.videoGravity = .resizeAspectFill
    playerViewController.showsPlaybackControls = showsPlaybackControls
    playerViewController.allowsPictureInPicturePlayback = allowsPictureInPicturePlayback
    if #available(iOS 14.0, *) {
      playerViewController.canStartPictureInPictureAutomaticallyFromInline = true
    }

    notificationCenter.addObserver(
      context.coordinator,
      selector: #selector(Coordinator.playerDidFinishPlaying),
      name: .AVPlayerItemDidPlayToEndTime,
      object: player?.currentItem
    )

    return playerViewController
  }

  func updateUIViewController(_ uiViewController: AVPlayerViewController, context: Context) {
    if isPlaying {
      uiViewController.player?.play()
    } else {
      uiViewController.player?.pause()
    }
  }

  func makeCoordinator() -> Coordinator {
    Coordinator(self)
  }

  class Coordinator {
    var parent: ContentView

    init(_ parent: ContentView) {
      self.parent = parent
    }

    deinit {
      parent.cleaup()
    }

    @objc
    func playerDidFinishPlaying() {
      parent.isPlaying = false
    }
  }
}

// MARK: - VideoPlayerView

public struct VideoPlayerView: View {
  @Binding private var isPlaying: Bool
  private let source: PlayerSource
  private let notificationCenter: NotificationCenter
  private let allowsPictureInPicturePlayback: Bool
  private let showsPlaybackControls: Bool
  private let accessibilityLabel: String

  public init(
    isPlaying: Binding<Bool>,
    url: URL,
    notificationCenter: NotificationCenter,
    allowsPictureInPicturePlayback: Bool = false,
    showsPlaybackControls: Bool = false,
    accessibilityLabel: String = ""
  ) {
    self._isPlaying = isPlaying
    self.source = .url(url)
    self.notificationCenter = notificationCenter
    self.allowsPictureInPicturePlayback = allowsPictureInPicturePlayback
    self.showsPlaybackControls = showsPlaybackControls
    self.accessibilityLabel = accessibilityLabel
  }

  public init(
    player: AVPlayer?,
    videoTitle: String,
    isPlaying: Binding<Bool> = .constant(true),
    notificationCenter: NotificationCenter = .default,
    allowsPictureInPicturePlayback: Bool = true,
    showsPlaybackControls: Bool = true,
    accessibilityLabel: String?
  ) {
    self._isPlaying = isPlaying
    self.source = .player(player)
    self.notificationCenter = notificationCenter
    self.allowsPictureInPicturePlayback = allowsPictureInPicturePlayback
    self.showsPlaybackControls = showsPlaybackControls
    if let altText = accessibilityLabel, !altText.isEmpty {
      self.accessibilityLabel = altText
    } else {
      self.accessibilityLabel = videoTitle
    }
  }

  public var body: some View {
    ContentView(
      source: source,
      isPlaying: $isPlaying,
      notificationCenter: notificationCenter,
      allowsPictureInPicturePlayback: allowsPictureInPicturePlayback,
      showsPlaybackControls: showsPlaybackControls
    )
    .frame(
      minWidth: 0,
      maxWidth: .infinity,
      minHeight: 0,
      maxHeight: .infinity
    )
    .accessibilityElement(children: .ignore)
    .accessibilityLabel(accessibilityLabel)
  }
}
