import OGL10n
import OGWebView
import SwiftUI

// MARK: - WebViewBackButtonImageStyle

public struct WebViewBackButtonImageStyle: OGWebViewBackButtonImageStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    OGImages.icon24x24ChevronLeftPrimary.image.renderingMode(.original)
      .accessibilityAddTraits(.isButton)
      .accessibilityLabel(ogL10n.General.Back)
  }
}

// MARK: - WebViewNavigationBarLogoStyle

public struct WebViewNavigationBarLogoStyle: OGWebViewNavigationBarLogoStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    OGImages.logoNavigationBar.image
      .renderingMode(
        .original
      )
      .accessibilityHidden(true)
  }
}

// MARK: - WebViewShareButtonStyle

public struct WebViewShareButtonStyle: OGWebViewShareButtonImageStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    HStack {
      Spacer(minLength: 10)
      OGImages.icon24x24SharePrimary.image
        .padding(UILayoutConstants.shareButtonPadding)
        .accessibilityAddTraits(.isButton)
        .accessibilityLabel(ogL10n.ProductDetail.Share.Button.Accessibility)
    }
  }
}

// MARK: - WebViewBackButtonStyle

public struct WebViewBackButtonStyle: OGWebViewBackButtonStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration.content
      .padding(UILayoutConstants.backButtonPadding)
  }
}

// MARK: - WebViewProgressViewStyle

public struct WebViewProgressViewStyle: OGWebViewProgressViewStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration.content
      .tint(OGColors.primaryPrimary100.color)
  }
}
