import OGL10n
import OGNavigationBar
import SwiftUI

// MARK: - NavigationBarSearchButtonStyle

public struct NavigationBarSearchButtonStyle: OGNavigationBarSearchButtonStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration
      .content
      .padding(UILayoutConstants.searchButtonPadding)
      .accessibilityAddTraits(.isButton)
      .accessibilityLabel(ogL10n.Search.Button.Accessibility)
  }
}

// MARK: - NavigationBarSearchButtonIconStyle

public struct NavigationBarSearchButtonIconStyle: OGNavigationBarSearchButtonIconStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    OGImages.icon24x24SearchPrimary.image
      .accessibility(hidden: true)
  }
}

// MARK: - NavigationBarSearchFieldIconStyle

public struct NavigationBarSearchFieldIconStyle: OGNavigationBarSearchFieldIconStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    OGImages.icon16x16Search.image
      .padding(EdgeInsets(top: 10, leading: 8, bottom: 10, trailing: 8))
      .accessibility(hidden: true)
  }
}

// MARK: - NavigationBarSearchFieldTextStyle

public struct NavigationBarSearchFieldTextStyle: OGNavigationBarSearchFieldTextStyle {
  let prompt: String
  public init(prompt: String) {
    self.prompt = prompt
  }

  public func makeBody(configuration: Configuration) -> some View {
    Text(prompt)
      .font(for: OGFonts.copyL)
      .foregroundColor(OGColors.backgroundBackground60.color)
      .accessibilityAddTraits(.isSearchField)
  }
}

// MARK: - NavigationBarSearchFieldStyle

public struct NavigationBarSearchFieldStyle: OGNavigationBarSearchFieldStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration
      .content
      .background(OGColors.backgroundBackground10.color)
      .cornerRadius(8.0)
      .padding(.horizontal, UILayoutConstants.horizontalPadding)
      .accessibilityAddTraits(.isSearchField)
      .accessibilityHint(ogL10n.General.DoubleTabToEdit.Accessibility)
  }
}

// MARK: - NavigationBarTopStyle

public struct NavigationBarTopStyle: OGNavigationBarTopStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration
      .content
      .background(OGColors.backgroundBarNavigationBar.color)
  }
}

// MARK: - NavigationBarStyle

public struct NavigationBarStyle: OGNavigationBarStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration
      .content
      .background(OGColors.backgroundBarNavigationBar.color)
    Divider()
      .background(OGColors.backgroundBackground40.color)
  }
}

// MARK: - NavigationBarBackButtonIconStyle

public struct NavigationBarBackButtonIconStyle: OGNavigationBarBackButtonIconStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    OGImages.icon24x24ChevronLeftPrimary.image
      .accessibilityAddTraits(.isButton)
      .accessibilityLabel(ogL10n.General.Back)
  }
}

// MARK: - NavigationBarBackButtonStyle

public struct NavigationBarBackButtonStyle: OGNavigationBarBackButtonStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration.content
      .padding(UILayoutConstants.backButtonPadding)
      .accessibilityAddTraits(.isButton)
      .accessibilityLabel(ogL10n.General.Back)
  }
}

// MARK: - NavigationBarTitleStyle

public struct NavigationBarTitleStyle: OGNavigationBarTitleStyle {
  public init() {}
  public func makeBody(configuration: Configuration) -> some View {
    configuration.content
      .padding(.trailing, UILayoutConstants.Default.padding)
      .font(for: OGFonts.titleM)
      .foregroundColor(OGColors.navigationBarElementTitle.color)
      .accessibilityAddTraits(.isHeader)
  }
}

extension UILayoutConstants {
  static let backButtonPadding = EdgeInsets(top: 14, leading: Self.horizontalPadding, bottom: 16, trailing: 0)
  static let searchButtonPadding = EdgeInsets(top: 14, leading: 0, bottom: 16, trailing: Self.horizontalPadding)
  static let horizontalPadding = CGFloat(16)
  static let shareButtonPadding = EdgeInsets(top: 14, leading: -14, bottom: 16, trailing: Self.horizontalPadding)
}
