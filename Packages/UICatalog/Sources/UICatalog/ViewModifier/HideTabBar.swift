import SwiftUI

extension View {
  public func hideTabBar() -> some View {
    modifier(HideTabBarModifier())
  }
}

// MARK: - HideTabBarModifier

struct HideTabBarModifier: ViewModifier {
  @State private var shouldShowTabBar = false

  func body(content: Content) -> some View {
    if #available(iOS 16.0, *) {
      content
        .onAppear {
          shouldShowTabBar = false
        }
        .onDisappear {
          shouldShowTabBar = true
        }
        .toolbar(shouldShowTabBar ? .visible : .hidden, for: .tabBar)
    } else {
      content
    }
  }
}
