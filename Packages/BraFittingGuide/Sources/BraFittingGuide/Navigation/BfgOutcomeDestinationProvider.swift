import OGCore
import OGNavigation
import SwiftUI

// MARK: - BraFittingGuideOutcomeDestinationProvider

public struct BfgOutcomeDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .braFittingGuideOutcome

  public init() {}

  public func provide(_: OGRoute) -> some View {
    OutcomeView()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
