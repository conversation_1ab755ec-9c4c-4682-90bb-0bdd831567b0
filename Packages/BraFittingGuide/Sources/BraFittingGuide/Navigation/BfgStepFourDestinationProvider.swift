import OGCore
import OGNavigation
import SwiftUI

// MARK: - BraFittingGuideStepFourDestinationProvider

public struct BfgStepFourDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .braFittingGuideStepFour

  public init() {}

  public func provide(_: OGRoute) -> some View {
    BraFittingGuideStepFour()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
