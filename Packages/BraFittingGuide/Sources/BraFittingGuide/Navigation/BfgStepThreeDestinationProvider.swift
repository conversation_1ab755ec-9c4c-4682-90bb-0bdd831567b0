import OGCore
import OGNavigation
import SwiftUI

// MARK: - BraFittingGuideStepThreeDestinationProvider

public struct BfgStepThreeDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .braFittingGuideStepThree

  public init() {}

  public func provide(_: OGRoute) -> some View {
    BraFittingGuideStepThree()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
