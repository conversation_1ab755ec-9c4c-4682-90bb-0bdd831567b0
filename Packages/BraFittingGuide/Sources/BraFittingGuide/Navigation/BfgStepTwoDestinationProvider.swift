import OGCore
import OGNavigation
import SwiftUI

// MARK: - BfgStepTwoDestinationProvider

public struct BfgStepTwoDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .braFittingGuideStepTwo

  public init() {}

  public func provide(_: OGRoute) -> some View {
    BraFittingGuideStepTwo()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
