import OGCore
import OGNavigation
import SwiftUI

// MARK: - BfgStepOneDestinationProvider

public struct BfgStepOneDestinationProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier = .braFittingGuide

  public init() {}

  public func provide(_: OGRoute) -> some View {
    BraFittingGuideStepOne()
  }

  public func presentationType() -> OGPresentationType {
    .fullScreenCover
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
