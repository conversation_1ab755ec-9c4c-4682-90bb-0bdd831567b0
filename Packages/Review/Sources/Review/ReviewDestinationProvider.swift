import Foundation
import OGCore
import OGDIService
import <PERSON>GRouter
import StoreKit
import Swift<PERSON>
import UICatalog
import UIKit

// MARK: - OGReviewDestinationProvider

public struct ReviewDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.review
  }

  public func provide(_ route: OGRoute) -> some View {
    if let activeWindowScene = UIApplication.shared.activeWindowScene {
      SKStoreReviewController.requestReview(in: activeWindowScene)
      router.dismiss()
    }
    return EmptyView()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
